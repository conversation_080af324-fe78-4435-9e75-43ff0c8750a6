from datetime import datetime,timedelta
from typing import Annotated, Any, Dict, List

from pydantic import BaseModel,Field


class StockNewsInput(BaseModel):
    ticker: Annotated[str, "The stock ticker symbol (e.g., AAPL, MSFT)."]
    start_date: Annotated[
        str,
        f"A filter to select the relevant document based on a start date. Break year into weeks. Current date is {datetime.now().date()}",
    ]
    end_date: Annotated[
        str,
        f"A filter to select the relevant document based on a end date. Break year into weeks. and end_date should be less than current date and Current date is: {datetime.now().date()}",
    ]
    limit: Annotated[
        int,
        "Specify the limit for press releases based on time duration: 5 per day, 10 per week, 20 per month, and 30 per year.",
    ]

class QuantitativeDataInput(BaseModel):
    """Quantitative data retrieval"""

    user_query: Annotated[
        str,
        "User augmented query from question.",
    ]

    asset_name: Annotated[
        str,
        "The name of the financial asset (e.g., Apple Inc., Microsoft Corporation). If not provided, try to extract it from the user's question.",
    ] = None
    ticker: Annotated[
        str,
        "The stock ticker symbol (e.g., AAPL, MSFT). If provided which is by default use it. If not provided, try to extract it from the user's question or map the asset_name to its corresponding ticker.",
    ] = None
    financial_metric: Annotated[
        str,
        """The financial metric needed to respond to the question, 
        If the financial metric is not specified, infer it based on the user's query and ensure all relevant aspects are covered.""",
    ] = None

    start_date: Annotated[
        str,
        "A filter to select the relevant document based on a start date. Defaults to 1 year ago from today."
    ] = Field(default_factory=lambda: (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d"))

    end_date: Annotated[
        str,
        "A filter to select the relevant document based on an end date. Date should not be greater than today. Defaults to today."
    ] = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"))
    
    chart_type: Annotated[
        str,
        "Extract chart type from query only these 'line', 'bar' and 'table', by default it is 'line'",
    ] = None

class Qualitative(BaseModel):
    ticker: Annotated[
        str,
        "The stock ticker symbol (e.g., AAPL, MSFT)."
    ]
    
    start_date: Annotated[
        str,
        "A filter to select the relevant document based on a start date. Defaults to 1 year ago from today."
    ] = Field(default_factory=lambda: (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d"))

    end_date: Annotated[
        str,
        "A filter to select the relevant document based on an end date. Date should not be greater than today. Defaults to today."
    ] = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"))

    augmented_user_query: Annotated[
        List[str],
        "For each user query, generate multiple distinct augmented queries by rephrasing or expanding on the original. Ensure that each version explores different facets of the topic."
    ]

    filling_type: Annotated[
        str,
        "Based on the user’s query, determine the appropriate filing type: 10-K (annual), 10-Q (quarterly), or 8-K (current report). If not specified, use both 10-K and 10-Q by default."
    ] = "ALL"