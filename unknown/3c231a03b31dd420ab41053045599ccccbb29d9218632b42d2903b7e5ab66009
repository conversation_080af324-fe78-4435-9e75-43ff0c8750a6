# 🧠 autogen-tradergpt

**Autogen-TraderGPT** is a modular AI-driven trading assistant framework designed for flexibility, automation, and clarity. Whether you're building AI agents, data pipelines, or simply want granular control over logging and debugging, this repo has tools to help you scale efficiently.

---

## 📦 Project Highlights

- ✅ **Custom Logging**: Inject logs anywhere in your codebase with a single import.
- 🤖 **AutoGen Integration**: Built with AI-agent-based architecture in mind.
- 🛠️ **Modular App Structure**: Clean and scalable directory layout.
- 📊 **Trading-Friendly Foundation**: Designed to support trading automation use cases.

---
# Requirements
```python
pip install -U "autogen-agentchat" "autogen-ext[openai,azure]"
```

## 🔍 Custom Logging Usage

Logging made simple, readable, and centralized.

Just import the logger and use it anywhere in your app:

```python
from app.logging_custom_directory.logger_custom import logger

logger.info("✅ Fetched users successfully")
logger.debug("🐛 Debug info for /users")
logger.error("❌ This is a test error log")
```

### 💡 Why use this logger?

- All logs are stored in a dedicated logging directory.
- Easy to trace app flow and debug issues quickly.
- Different log levels supported: `info`, `debug`, `warning`, `error`, and `critical`.

---

## 🚀 Getting Started

Coming soon: Setup instructions, environment configs, and usage examples for the trading agent.

---

## 🧰 Directory Overview (Work in Progress)

```
autogen-tradergpt/
├── DockerFile
├── README.md
├── app
│   ├── __init__.py
│   ├── agent
│   │   ├── agent_creation.py
│   │   └── calling_agent.py
│   ├── api
│   │   ├── __init__.py
│   │   ├── api.py
│   │   ├── api_payload_schema.py
│   │   └── generate_reply.py
│   ├── core
│   │   ├── __init__.py
│   │   └── config.py
│   ├── db
│   │   ├── __init__.py
│   │   └── session.py
│   ├── logging_custom_directory
│   │   ├── __init__.py
│   │   └── logger_custom.py
│   ├── main.py
│   ├── schemas
│   │   └── __init__.py
│   ├── services
│   │   └── __init__.py
│   └── tools
│       ├── internal_search
│       ├── web_search
│       └── workflows
├── logs
│   ├── 2025-04-05
│   └── 2025-04-06
├── requirements.txt
├── run.py
└── structure.txt

```

---

## 📌 To-Do

- [ ] Add Web search tool and agent
- [ ] Add internal tool and agent
- [ ] Add workflows
- [ ] Add Docker support

---

Stay tuned as we evolve this into a fully functional AI trading co-pilot! 🚀
