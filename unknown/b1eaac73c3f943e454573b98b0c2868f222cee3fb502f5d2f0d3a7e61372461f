import os
import json

from dotenv import load_dotenv

load_dotenv()

CUSTOM_STOP_WORDS = [
    "a",
    "an",
    "and",
    "the",
    "in",
    "is",
    "it",
    "of",
    "on",
    "at",
    "to",
    "for",
    "with",
    "by",
    "as",
    "this",
    "that",
    "from",
    "but",
    "are",
    "was",
    "have",
    "has",
    "had",
    "been",
    "be",
    "if",
    "or",
    "not",
    "nt",
    "you",
    "your",
    "he",
    "him",
    "his",
    "she",
    "her",
    "hers",
    "we",
    "our",
    "us",
    "they",
    "them",
    "their",
    "i",
    "me",
    "my",
    "mine",
    "do",
    "does",
    "did",
    "doing",
    "will",
    "would",
    "should",
    "could",
    "can",
    "may",
    "might",
    "must",
    "shall",
    "into",
    "up",
    "out",
    "about",
    "over",
    "under",
    "above",
    "below",
    "between",
    "among",
    "through",
    "during",
    "before",
    "after",
    "above",
    "below",
    "both",
    "either",
    "neither",
    "nor",
    "while",
    "since",
    "until",
    "so",
    "because",
    "since",
    "why",
    "how",
    "what",
    "when",
    "where",
    "who",
    "which",
    "whom",
    "whose",
    "those",
    "these",
    "other",
    "some",
    "more",
    "most",
    "few",
    "many",
    "all",
    "any",
    "each",
    "every",
    "own",
    "same",
    "such",
    "no",
    "yes",
    "than",
    "too",
    "very",
    "s",
    "t",
]
db_conventional_string = os.getenv("DATABASE_URL")
alphavantage_api = os.getenv("ALPHAVANTAGE_API_KEY")
vector_file_path = "app/tools/internal_search/quantitative_analysis_tool/models/metric_vector.npy"
json_mapper_path = "app/tools/internal_search/quantitative_analysis_tool/models/metric_mapper.json"
gcp_credential = os.getenv("GCP_FILE_CRED")

# Function to safely parse GCP credentials
def get_gcp_credentials_dict():
    """Parse the GCP_FILE_CRED string into a dictionary."""
    try:
        # Replace literal \n with actual newlines to fix JSON parsing
        if gcp_credential:
            # First, normalize newlines in the JSON string
            normalized_cred = gcp_credential.replace('\\n', '\n')
            return json.loads(normalized_cred)
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing GCP_FILE_CRED: {e}")
        return {}