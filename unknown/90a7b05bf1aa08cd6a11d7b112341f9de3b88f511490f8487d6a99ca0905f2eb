from typing import Annotated, Any, Dict, List

from app.tools.web_search import agent_tool_schema
from gpt_researcher.agent import <PERSON><PERSON>esearcher

import os
from dotenv import load_dotenv


async def get_news(
    extracted_input: Annotated[
        agent_tool_schema.GetNewsInput,
        f"This dictionary includes: 'query' which is used to look for news ",
    ],
) -> List[Dict[str, str]]:
    """Get recent news articles about a company"""
    extracted_input
    news_article = [
        {
            "title": "Tesla Expands Cybertruck Production",
            "date": "2024-03-20",
            "summary": "Tesla ramps up Cybertruck manufacturing capacity at Gigafactory Texas, aiming to meet strong demand.",
        },
        {
            "title": "Tesla FSD Beta Shows Promise",
            "date": "2024-03-19",
            "summary": "Latest Full Self-Driving beta demonstrates significant improvements in urban navigation and safety features.",
        },
        {
            "title": "Model Y Dominates Global EV Sales",
            "date": "2024-03-18",
            "summary": "Tesla's Model Y becomes best-selling electric vehicle worldwide, capturing significant market share.",
        },
    ]
    return news_article



async def web_search_deep( extracted_input: Annotated[
        agent_tool_schema.GetNewsInput,
        """This dictionary includes: 'query' which is used to to execute in-depth web inquiries. 
        It goes beyond standard data collection by actively aggregating information from a wide range of digital resources, 
        extracting key insights through advanced summarization techniques, and assembling a cohesive research report. 
        This tool is tailored for intricate queries that demand extensive, up-to-date data and rigorous analysis,
        ensuring that every report is comprehensive, accurate, and rich in contextual detail.""",
    ],) -> str:
    
    
    """
    This dictionary includes: 'query' which is used to to execute in-depth web inquiries. 
        It goes beyond standard data collection by actively aggregating information from a wide range of digital resources, 
        extracting key insights through advanced summarization techniques, and assembling a cohesive research report. 
        This tool is tailored for intricate queries that demand extensive, up-to-date data and rigorous analysis,
        ensuring that every report is comprehensive, accurate, and rich in contextual detail
    Performs a deep web search using GPTResearcher for the given query.
    Returns a research report string.
    Use this tool for complex questions requiring up-to-date, comprehensive information or research.
    """
    load_dotenv()  # Load environment variables from .env file if needed
    print(f"\n--- Calling GPTResearcher for query: '{extracted_input}' ---")
    try:
        # Ensure API key is available for the researcher instance if needed dynamically
        # Although loaded via dotenv, explicitly checking doesn't hurt
        if not os.environ.get("OPENAI_API_KEY"):
             print("Warning: OPENAI_API_KEY not found in environment for GPTResearcher.")
             # return "Error: OPENAI_API_KEY not configured for researcher." # Option A: return error
             # raise ValueError("OPENAI_API_KEY not configured.") # Option B: raise exception
        
        researcher = GPTResearcher(query=extracted_input, report_type="research_report",report_source="web") # Pass API key explicitly if needed
        # researcher = GPTResearcher(query=query, report_type="research_report", report_source="web") # Original line

        # Conduct research (blocking potentially, consider asyncio.to_thread if it blocks the event loop)
        # await researcher.conduct_research() # Original async call - keep if works
        # If conduct_research is blocking synchronous code:
        research_conduct = await researcher.conduct_research() 

        # Write report (blocking potentially, consider asyncio.to_thread if it blocks the event loop)
        # report = await researcher.write_report() # Original async call - keep if works
        # If write_report is blocking synchronous code:
        # report = await researcher.write_report()

        print(f"--- GPTResearcher finished ---")
        return research_conduct
    except Exception as e:
        print(f"Error during GPTResearcher execution: {e}")
        return f"Error performing web research: {e}"
