database_name = 'officefield_local'
SQL_WRITE_RPC = """select insert_chat_conversations_v8(%(user_id)s,
                                                %(symbol)s,
                                                %(tone)s,
                                                %(placable_vector_extrated_data)s,
                                                %(intial_conversation)s,
                                                %(templates)s,
                                                %(inputs)s,
                                                %(outputs)s,
                                                %(input_token)s,
                                                %(output_token)s,
                                                %(model_name)s,
                                                %(feedback_parity)s,
                                                %(feedback_explanation)s,
                                                %(data)s,
                                                %(mapped_features)s,
                                                %(created)s,
                                                %(status_var)s,
                                                %(chat_id)s)"""

SQL_READ_RPC = """select chat_conversations_format_updated_version_2(%(ask)s, %(user_id)s, %(symbol)s, %(chat_id)s, 'limit',2)"""

#SQL_READ_RPC_WITH_CHAT = """select chat_conversations_format_updated_version_3(%(ask)s, %(user_id)s, %(symbol)s, %(chat_id)s, 'limit', 2)"""

SQL_SEARCH_QUERY = """select public.fetch_symbol_from_question(%(asset_name)s, %(financial_metric)s)"""

SQL_CONTEXT_RPC = """select read_context_v1(%(symbol)s,%(user_id)s,%(chat_id)s,%(ask)s,5)"""

#SQL_CONTEXT_RPC_WITH_CHAT = """select read_context_v1_with_chat(%(symbol)s,%(user_id)s,%(chat_id)s,%(ask)s,5)"""

SQL_CONTEXT_RPC_TESTING = """select read_context_v1_testing(%(symbol)s,%(user_id)s,%(chat_id)s,5)"""

#SQL_CONTEXT_RPC_TESTING_WITH_CHAT = """select read_context_v1_testing_with_chat(%(symbol)s,%(user_id)s,%(chat_id)s,5)"""

SQL_FETCHED_TEMPLATE = """select fetched_template(%(symbol)s,3,%(user_id)s,%(chat_id)s)"""

#SQL_FETCHED_TEMPLATE_WITH_CHAT = """select fetched_template_with_chat(%(symbol)s,3,%(user_id)s,%(chat_id)s)"""
