import datetime
from typing import List, Optional, Literal, TypedDict
from langchain_core.language_models.chat_models import BaseChatModel

from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.types import Command
from langchain_core.messages import HumanMessage, trim_messages


class State(MessagesState):
    next: str


def make_supervisor_node(llm: BaseChatModel, members: list[str]) -> str:
    options = ["FINISH"] + members

    
    system_prompt = f"""
            You are a supervisor tasked with managing a conversation between the
         following workers: {members}. Given the following user request,
         respond with the worker to act next. Each worker will perform a
         task and respond with their results and status.If you get a corrent answer from any other tool pass it to writer. Use web_researcher member as a last priority if you dont get results from other members. 
         When finished respond with FINISH.
         
        ### Rules:
            - Always plan research before delegating.
            - **Delegate to one agent at a time** based on the research focus.
            - **Never send final answers directly to the user**; delegate the output to the **Writer worker** for finalization.
            - **Writer working** is responsible for creating the final user-facing output .
          ### Strict Rules:
          - **Refer to today's date** ({str(datetime.date.today())}) during planning for time-sensitive analysis.
    """

    class Router(TypedDict):
        """Worker to route to next. If no workers needed, route to FINISH."""

        next: Literal[*options]

    def supervisor_node(state: State) -> Command[Literal[*members, "__end__"]]:
        """An LLM-based router."""
        messages = [
            {"role": "system", "content": system_prompt},
        ] + state["messages"]
        response = llm.with_structured_output(Router).invoke(messages)
        goto = response["next"]
        if goto == "FINISH":
            goto = END

        return Command(goto=goto, update={"next": goto})

    return supervisor_node


