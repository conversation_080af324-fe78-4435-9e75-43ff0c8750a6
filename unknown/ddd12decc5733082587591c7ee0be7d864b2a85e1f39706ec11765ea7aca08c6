from autogen_core.tools import FunctionTool
from app.tools.workflows.helper import comprehensive_company_analysis, market_news_analysis, market_movers_analysis, analyst_sentiment_analysis, technical_analysis, earnings_and_transcript_analysis, valuation_dcf_analysis, esg_sustainability_analysis, macro_economic_sector_analysis, etf_mutual_fund_holding_analysis, market_data_technical_analysis, insider_Trading_institutional_ownership_analysis, dividend_strategy_income_analysis, merger_and_acquisition_analysis, ipo_market_trends_analysis, forex_and_commodity_trading_analysis, crypto_market_sentiment_price_analysis

comprehensive_company_analysis_tool = FunctionTool(
    func=comprehensive_company_analysis,
    name="comprehensive_company_analysis_tool",
    description="""Conducts an in-depth financial analysis of a company, including a business overview, stock performance, price target trends, 
                   key financial metrics, and analyst sentiment. Summarizes the company's current financial health and market positioning."""
)

# Convert the function to a FunctionTool
market_news_tool = FunctionTool(
    func=market_news_analysis,
    name="market_news_tool",
    description= """Analyzes recent financial and economic news across key categories, including stock market updates, cryptocurrency, forex, 
                    general market sentiment, and press releases. Extracts key headlines and insights to provide a concise market overview."""
)

# Convert the function to a FunctionTool
market_movers_analysis_tool = FunctionTool(
    func=market_movers_analysis,
    name="market_movers_analysis_tool",
    description="""Analyzes daily market movers by identifying the top gainers, top losers, and most actively traded stocks. 
                    Combines price performance with relevant news and press releases to explain the factors influencing market movement."""
)

# Convert the function to a FunctionTool
analyst_sentiment_tool = FunctionTool(
    func=analyst_sentiment_analysis,
    name="analyst_sentiment_tool",
    description="""Analyzes analyst sentiment for a given company by evaluating recent upgrades and downgrades.
                    price target trends, analyst ratings, and related financial news. 
                    Returns a concise summary of market perception and expert outlook."""
)

technical_analysis_tool = FunctionTool(
    func=technical_analysis,
    name="technical_analysis_tool",
    description="""Performs advanced technical analysis on a given stock using chart patterns, price action, and key indicators such as MACD, RSI, 
                   Bollinger Bands, and Moving Averages. Evaluates short-term trends, momentum shifts, volatility, and potential reversal signals. 
                   Ideal for traders seeking signal-based insights beyond fundamentals and market sentiment."""
)

earnings_and_transcript_analysis_tool = FunctionTool(
    func=earnings_and_transcript_analysis,
    name="earnings_and_transcript_analysis",
    description="""Analyzes a company's earnings report and earnings call transcript to provide key financial metrics, 
                   such as revenue, EPS, and profit margins. Extracts insights on product performance, market strategy, 
                   and future outlook, helping investors assess financial health and growth potential."""
)

valuation_dcf_analysis_tool = FunctionTool(
    func=valuation_dcf_analysis,
    name="valuation_dcf_analysis",
    description="""Use this to perform DCF (Discounted Cash Flow) valuation, analyzing a company’s financial metrics like P/E, P/B ratios, and debt to estimate its intrinsic value and determine if the stock is fairly valued, overvalued, or undervalued."""
)

esg_sustainability_analysis_tool = FunctionTool(
    func=esg_sustainability_analysis,
    name="esg_sustainability_analysis",
    description="""Use this to evaluate a company's Environmental, Social, and Governance (ESG) performance, including sustainability practices, social responsibility initiatives, governance structures, and the company's overall impact on the environment and society."""
)

macro_economic_sector_analysis_tool = FunctionTool(
    func=macro_economic_sector_analysis,
    name="macro_economic_sector_analysis",
    description="""Use this to analyze macroeconomic trends, sector performance, and industry data, combining key economic indicators and sector-specific insights to identify growth opportunities and potential risks for businesses."""
)

etf_mutual_fund_holding_analysis_tool = FunctionTool(
    func=etf_mutual_fund_holding_analysis, 
    name="etf_mutual_fund_holding_analysis",
    description="""Use this to analyze ETF and mutual fund holdings, providing insights into allocation, fund performance, and strategic exposures across sectors and regions, helping to identify trends and investor sentiment."""
)

market_data_technical_analysis_tool = FunctionTool(
    func=market_data_technical_analysis, 
    name="market_data_technical_analysis",
    description="""Use this to analyze real-time stock data with technical indicators like moving averages, RSI, ADX, and volume to generate actionable insights for intraday trading, trend analysis, and entry/exit strategies."""
)

insider_Trading_institutional_ownership_analysis_tool = FunctionTool(
    func=insider_Trading_institutional_ownership_analysis, 
    name="insider_Trading_institutional_ownership_analysis",
    description="""Use this to analyze insider trading activities and institutional ownership changes, providing insights into the sentiment and confidence of company executives and large investors regarding a company's future prospects."""
)

dividend_strategy_income_analysis_tool = FunctionTool(
    func=dividend_strategy_income_analysis, 
    name="dividend_strategy_income_analysis",
    description="""Use this to analyze a company's dividend strategy, including yield, payout ratio, historical performance, and comparisons with peers, helping investors assess income stability and long-term growth potential."""
)

merger_and_acquisition_analysis_tool = FunctionTool(
    func=merger_and_acquisition_analysis, 
    name="merger_and_acquisition_analysis",
    description="""Use this to analyze recent mergers and acquisitions, assessing market impact, sector performance, consolidation trends, and investment opportunities, providing insights into strategic shifts and potential risks in the market."""
)

ipo_market_trends_analysis_tool = FunctionTool(
    func=ipo_market_trends_analysis, 
    name="ipo_market_trends_analysis",
    description="""Use this to analyze recent and upcoming IPOs, evaluate new market entrants, assess their financial fundamentals, and understand their potential impact on market dynamics and established companies."""
)

forex_and_commodity_trading_analysis_tool = FunctionTool(
    func=forex_and_commodity_trading_analysis, 
    name="forex_and_commodity_trading_analysis",
    description="""Use this to develop trading strategies for forex pairs and commodities by integrating real-time and historical data, technical indicators, and risk management techniques to generate actionable insights for market entries and exits."""
)

crypto_market_sentiment_price_analysis_tool = FunctionTool(
    func=crypto_market_sentiment_price_analysis, 
    name="crypto_market_sentiment_price_analysis",
    description="""Use this to analyze cryptocurrency markets by integrating real-time pricing, historical trends, sentiment data, and technical indicators to identify potential trading opportunities and market movements."""
)


