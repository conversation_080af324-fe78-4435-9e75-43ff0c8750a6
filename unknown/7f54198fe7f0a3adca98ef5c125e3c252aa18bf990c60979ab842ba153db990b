# Chat ID Implementation Guide

This document outlines the changes made to implement multiple chat sessions per user in the TraderGPT application.

## Overview

The chat_id feature allows users to have multiple chat sessions, similar to how modern chat applications work. Each user can have multiple conversations, and they can switch between them while maintaining separate chat histories.

## Code Changes

### 1. API Payload Schema

**File:** `app/api/api_payload_schema.py`

Added `chat_id` field to the `UserAskRequest` model:

```python
chat_id: Optional[str] = Field(
    None,
    description="The chat ID to identify which conversation this message belongs to"
)
```

### 2. Database Layer

**File:** `app/db/db.py`

Added new methods:

- `generate_chat_id(user_id)`: Creates a unique chat_id for a user
- `get_user_chats(user_id)`: Lists all chat_ids for a user
- `get_chat_details(chat_id)`: Gets details of a specific chat

Updated existing methods to support chat_id:

- `read_conversations_from_conventional_db(ask, user_id, symbol, chat_id=None)`
- `read_data(user_id, symbol, user_ask, chat_id=None)`
- `read_data_testing(user_id, symbol, chat_id=None)`
- `fetche_template_context(user_id, symbol, chat_id=None)`
- `update_read_data_testing(user_id, symbol, chat_id=None, limit=5)`
- `write_response(data)` - Now accepts chat_id in the data dictionary

### 3. SQL Parameters

**File:** `app/db/parameters.py`

Added new SQL queries with chat_id support:

- `SQL_READ_RPC_WITH_CHAT`
- `SQL_CONTEXT_RPC_WITH_CHAT`
- `SQL_CONTEXT_RPC_TESTING_WITH_CHAT`
- `SQL_FETCHED_TEMPLATE_WITH_CHAT`

Updated `SQL_WRITE_RPC` to use `insert_chat_conversations_v7` which includes chat_id.

### 4. Orchestration Layer

**File:** `app/core/Orchistration.py`

Updated the `history_conversation` method to accept and use chat_id:

```python
def history_conversation(self, user_id, symbol, SqlTool, chat_id=None):
    # Implementation now uses chat_id
```

### 5. Agent Layer

**File:** `app/agent/calling_agent.py`

Updated methods to support chat_id:

- `generating_reply(task, symbol, user_id, report, deep_search, history, chat_id=None, verbose=False)`
- `ReplyStream.generate(task, symbol, user_id, report, deep_search, history, chat_id=None, verbose=False)`

Modified state saving/loading to include chat_id in the filename.

### 6. API Endpoints

**File:** `app/api/generate_reply.py`

Updated existing endpoints:

- `/user_ask`: Now handles chat_id and generates a new one if not provided
- `/user_ask_stream`: Now handles chat_id and generates a new one if not provided

Added new endpoints:

- `GET /user_chats`: Lists all chat sessions for the authenticated user
- `GET /chat/{chat_id}`: Gets details of a specific chat session

## How to Use the Chat ID Feature

### 1. Automatic Chat Management

The system automatically manages chat sessions:

- If a user makes a request without a chat_id, the system will:
  - Look for an existing chat for that user and symbol
  - Use the existing chat if found
  - Create a new chat only if no existing chat is found

- Chat IDs include the user_id and symbol for better organization:
  ```
  user123_AAPL_20230815123456
  ```

### 2. Creating a New Chat Explicitly

To explicitly start a new conversation (even if one exists for the symbol):

```
POST /api/new_chat
```

Request body:
```json
{
  "symbol": "AAPL",
  "task": "optional initial message"
}
```

Response:
```json
{
  "user_id": "user123",
  "symbol": "AAPL",
  "chat_id": "user123_AAPL_20230815123456",
  "created": "2023-08-15T12:34:56.789Z"
}
```

### 3. Continuing an Existing Chat

To continue an existing conversation, include the chat_id in your request:

```json
{
  "task": "What about their latest earnings?",
  "symbol": "AAPL",
  "chat_id": "user123_AAPL_20230815123456",
  "report": false,
  "deep_search": false
}
```

### 4. Listing User's Chats

To get a list of all chat sessions for a user:

```
GET /api/user_chats
```

Response:
```json
{
  "user_id": "user123",
  "chats": [
    "user123_AAPL_20230815123456",
    "user123_MSFT_20230814093045"
  ]
}
```

### 5. Getting Chat Details

To get details of a specific chat:

```
GET /api/chat/user123_AAPL_20230815123456
```

Response:
```json
{
  "chat_id": "user123_AAPL_20230815123456",
  "messages": [
    {
      "id": 123,
      "user_id": "user123",
      "symbol": "AAPL",
      "input": "Tell me about Apple stock",
      "output": "...",
      "created": "2023-08-15T12:34:56"
    },
    {
      "id": 124,
      "user_id": "user123",
      "symbol": "AAPL",
      "input": "What about their latest earnings?",
      "output": "...",
      "created": "2023-08-15T12:36:22"
    }
  ]
}
```

## Implementation Notes

1. **Backward Compatibility**: The system still works without chat_id for backward compatibility.

2. **Smart Chat ID Management**:
   - If no chat_id is provided, the system looks for an existing chat for that user and symbol
   - Only creates a new chat_id if no existing chat is found
   - Users can explicitly create a new chat using the `/new_chat` endpoint

3. **Meaningful Chat IDs**: Chat IDs include the symbol (e.g., `user123_AAPL_20230815123456`) for better organization and readability.

4. **State Management**: Chat state files are now named with the chat_id to keep separate states for each chat.

5. **Database Filtering**: All database queries now filter by chat_id when provided.

6. **Persistence**: Chat sessions persist across application restarts since they're stored in the database.

