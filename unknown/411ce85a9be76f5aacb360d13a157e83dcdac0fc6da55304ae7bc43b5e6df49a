import json
import re
from datetime import datetime

import joblib
import psycopg2
import requests
# from langchain_community.embeddings import HuggingFaceEmbeddings
from sentence_transformers import SentenceTransformer
from psycopg2 import sql
from sklearn.metrics.pairwise import cosine_similarity

from app import db_conventional_string
from . import metric_search

SM = metric_search.MetricSearch()


class QuanitativeTool:
    def __init__(
        self,
        alphavantage_api,
        connection_string,
        stop_words,
        vector_file_path,
        mapper_file,
    ):
        self.alphavantage_api = alphavantage_api
        self.connection_string = connection_string
        self.CUSTOM_STOPWORDS = stop_words
        self.metric_vector = joblib.load(vector_file_path)
        self.embed_model = self.load_model()
        self.mapper_data = self.json_mapper(mapper_file)

    def json_mapper(self, mapper_file):
        with open(mapper_file, "r") as f:
            data = json.load(f)
            return data

    # def load_model(self):
    #     model = HuggingFaceEmbeddings(
    #         model_name="sentence-transformers/all-MiniLM-L6-v2",
    #         model_kwargs={"device": "cpu"},
    #     )

    #     return model
    def load_model(self):
        model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')

        return model

    def preprocess_text(self, text):
        processed_text = ""
        text = re.sub("[^a-zA-Z]", " ", text)
        text = re.sub("\s+", " ", text)
        # text = text.strip()
        text = text.lower()

        for word in text.split():
            if not word in self.CUSTOM_STOPWORDS:
                processed_text += word + " "

        return processed_text[:-1]

    def search_stocks(self, stock_names):
        symbols = []
        for stock_name in stock_names:
            url = f"https://www.alphavantage.co/query?function=SYMBOL_SEARCH&keywords={stock_name}&apikey={self.alphavantage_api}"
            response = requests.get(url)
            data = response.json()

            if "bestMatches" in data:
                best_matches = data["bestMatches"][0:5]
                for match in best_matches:
                    symbol = match["1. symbol"]
                    name = match["2. name"]
                    if "." not in symbol:
                        symbols.append((symbol, name))
            else:
                print(f"No matching symbols found for '{stock_name}'.")

        return symbols

    def search_metrics_from_vectors(self, metric, embedded_data, processed_texts):
        # embed_querry = self.embed_model.embed_query(metric)
        embed_querry = self.embed_model.encode(metric)
        similarities = cosine_similarity([embed_querry], embedded_data)[0]

        # Find the document(s) with the highest similarity
        most_similar_index = similarities.argmax()
        most_similar_text = processed_texts[most_similar_index]
        max_similarity = similarities[most_similar_index]

        # Only return the most similar text if the similarity is greater than 0.5
        return most_similar_text
        # if max_similarity > 0.5:
        #     return most_similar_text
        # else:
        #     return None

    def simple_metric_graph_data(self, data, time_interval, chart_type):
        transformed_data = {"metrics": {}}
        for metric in data:
            try:
                for entry in metric:
                    if entry["symbol"]["metric"] == "news":
                        metric = entry["symbol"]["metric"]
                        name = entry["symbol"]["name"]
                        title = entry["symbol"]["data"]["title"]
                        link = entry["symbol"]["data"]["link"]
                        # print(entry['symbol'])
                        # displayable_type = adjust_displayable(values)
                        transformed_data["metrics"].setdefault(metric, []).append(
                            {
                                "name": name,
                                "metric": metric,
                                "title": title,
                                "link": link,
                                "time_interval": time_interval,
                                "type": chart_type,
                            }
                        )
                    else:
                        metric = entry["symbol"]["metric"]
                        name = entry["symbol"]["name"]
                        values = entry["symbol"]["values"]
                        # print(entry['symbol'])
                        dates = entry["symbol"]["dates"]
                        # displayable_type = adjust_displayable(values)
                        transformed_data["metrics"].setdefault(metric, []).append(
                            {
                                "name": name,
                                "metric": metric,
                                "values": values,
                                "dates": dates,
                                "time_interval": time_interval,
                                "type": chart_type,
                            }
                        )
            except Exception as e:
                # print(e)
                pass
        return transformed_data

    def read_data_helper(self, start_date, end_date, symbols, metrics):
        try:
            conn = psycopg2.connect(self.connection_string)

            q = """select merged_function_date_range(%(symbols)s, %(start_date)s, %(end_date)s, %(simple_metrics)s)"""
            insert_query = sql.SQL(q)
            cursor = conn.cursor()
            cursor.execute(
                insert_query,
                {
                    "symbols": symbols,
                    "start_date": start_date,
                    "end_date": end_date,
                    "simple_metrics": json.dumps(metrics),
                },
            )
            # print(cursor.fetchall())
            rows = cursor.fetchall()[0][0]
            conn.close()
        except Exception as e:
            print(e)
            conn.close()
            rows = None
        return rows

    def validate_financial_metric(self, raw_metrics):
        preprocessed_raw_metrics = [
            self.preprocess_text(raw_metric) for raw_metric in raw_metrics
        ]

        metrics = []
        for metric in preprocessed_raw_metrics:
            data = self.search_metrics_from_vectors(
                metric=metric,
                embedded_data=self.metric_vector["embed"],
                processed_texts=self.metric_vector["text"],
            )
            if data is not None:
                metrics.append(data)
        return metrics

    def fetch_symbols_titles(self, company):
        try:
            conn = psycopg2.connect(db_conventional_string)
            cursor = conn.cursor()

            query = """SELECT public.fetch_symbols_titles(%(title)s)"""
            insert_query = sql.SQL(query)

            cursor.execute(insert_query, {"title": company})
            result = cursor.fetchall()

            data = []
            for row in result:
                for item in row:
                    if isinstance(item, list):
                        for record in item:
                            symbol = record.get("symbol")
                            title = record.get("title")
                            data.append((symbol, title))

        except Exception as e:
            print(f"An error occurred: {e}")
            conn.close()
            data = None
        return data

    def fetch_data(
        self,
        ticker,
        company_name,
        user_query,
        financial_metrics,
        start_date,
        end_date,
        chart_type,
    ):
        global mapper_metrics

        if isinstance(ticker, str):
            ticker = [ticker]

        if isinstance(financial_metrics, str):
            financial_metrics = financial_metrics

        if isinstance(company_name, str):
            company_name = [company_name]

        if isinstance(chart_type, list):
            chart_type = chart_type[0]
            if chart_type == None or chart_type == "None":
                chart_type = "line"

        # ticker[0] = fetch_symbols_titles(ticker[0])
        try:
            company_info = self.fetch_symbols_titles(ticker[0].upper())[0]
            if company_info is not None:
                symbol = [company_info[0]]
        except:
            company_info = self.fetch_symbols_titles(company_name[0].lower())[0]
            if company_info is not None:
                symbol = [company_info[0]]

        # helper function to fetch data according to start date and end date
        # validated_financial_metrics = self.validate_financial_metric(financial_metrics)[0]
        validated_financial_metrics = SM.semantic_search_agent(user_ask=user_query)
        # validated_financial_metrics = financial_metrics

        print("ticker: ", ticker, " ", symbol)
        print("company_name: ", company_name)
        print("validated_financial_metrics", validated_financial_metrics)
        print("financial_metrics: ", financial_metrics)
        print("start_date: ", start_date)
        print("end_date:", end_date)
        print("chart_type:", chart_type)

        # Fetch data from RPC
        try:
            data = []
            for metric in validated_financial_metrics:
                print(metric)
                feched_data = self.read_data_helper(
                    start_date=start_date, end_date=end_date, symbols=ticker, metrics=metric
                )
                data.append(feched_data)
            print(data)
            if chart_type is None:
                chart_type = "line"
            structured_data = self.simple_metric_graph_data(
                data, time_interval="yr", chart_type=chart_type
            )

            return [structured_data]
        except Exception as e:
            print(e)
