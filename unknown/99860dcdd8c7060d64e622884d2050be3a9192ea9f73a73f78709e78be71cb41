# from fastapi import APIRouter

# from app.api import generate_reply

# api_router = APIRouter()
# api_router.include_router(generate_reply.router, tags=["Users"])

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import generate_reply

app = FastAPI()

# ✅ Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # ⚠️ Replace with specific origins in production
    allow_methods=["*"],
    allow_headers=["*"],
)

# ✅ Register routers
app.include_router(generate_reply.router, tags=["Users"])
