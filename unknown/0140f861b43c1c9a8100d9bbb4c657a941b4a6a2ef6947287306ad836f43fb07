from app.tools.workflows.Program_Files.ta_strategies_TVLibrary import *
from app.core.config import settings

import pandas as pd
import numpy as np
import math

import openai

openai.api_key = settings.OPENAI_API_KEY


class AllStrategies:
    def __init__(self):
        # List all strategy classes (assumed imported/defined elsewhere)
        strategy_classes = [
            AberrationStrategies,
            AbsolutePriceOscillatorStrategies,
            AccelerationBandsStrategies,
            AccumulationDistributionLineStrategies,
            AccumulationDistributionIndexStrategies,
            AccumulationDistributionOscillatorStrategies,
            AdaptivePriceZoneStrategies,
            AllMovingAverageStrategies,
            ArcherMovingAveragesTrendsStrategies,
            ArcherOnBalanceVolumeStrategies,
            ArnaudLegouxMovingAverageStrategies,
            AroonStrategies,
            AroonOscillatorStrategies,
            AverageDirectionalIndexStrategies,
            AveragePriceStrategies,
            AverageTrueRangeStrategies,
            AwesomeOscillatorStrategies,
            BalanceOfPowerStrategies,
            # BetaStrategies,
            BiasStrategies,
            BRARStrategies,
            BollingerBandsStrategies,
            BollingerBandsWidthStrategies,
            BullBearPowerStrategies,
            BuyAndSellPressureStrategies,
            CenterOfGravityStrategies,
            ChandeForecastOscillatorStrategies,
            ChandeKrollStopStrategies,
            ChandeMomentumOscillatorStrategies,
            ChandelierExitStrategies,
            ChaikinADLineStrategies,
            ChaikinADOscillatorStrategies,
            ChaikinMoneyFlowStrategies,
            ChaikinOscillatorStrategies,
            ChoppinessIndexStrategies,
            CommodityChannelIndexStrategies,
            CorrelationTrendIndicatorStrategies,
            CoppockCurveStrategies,
            CumulativeForceIndexStrategies,
            CrossSignalsStrategies,
            DecayStrategies,
            DecreasingPriceStrategies,
            DetrendedPriceOscillatorStrategies,
            DirectionalMovementStrategies,
            DonchianChannelStrategies,
            DoubleExponentialMovingAverageStrategies,
            EhlersSuperSmootherFilterStrategies,
            ElderRayIndexStrategies,
            EldersForceIndexStrategies,
            EldersThermometerStrategies,
            ElasticVolumeMovingAverageStrategies,
            ElasticVolumeMACDStrategies,
            ExponentialMovingAverageStrategies,
            FibonacciPivotPointsStrategies,
            FibonacciWeightedMovingAverageStrategies,
            FiniteVolumeElementStrategies,
            FisherTransformStrategies,
            ForceIndexStrategies,
            FractalAdaptiveMovingAverageStrategies,
            GannHighLowActivatorStrategies,
            HighLowAverageStrategies,
            HilbertTransformDominantCyclePeriodStrategies,
            HilbertTransformDominantCyclePhaseStrategies,
            HilbertTransformInstantaneousTrendlineStrategies,
            HilbertTransformPhasorComponentsStrategies,
            HilbertTransformSineWaveStrategies,
            HilbertTransformTrendCycleStrategies,
            HoltWinterChannelStrategies,
            HoltWinterMovingAverageStrategies,
            HullExponentialMovingAverageStrategies,
            HullMovingAverageStrategies,
            IchimokuCloudStrategies,
            IncreasingPriceStrategies,
            InertiaStrategies,
            InverseFisherTransformRSIStrategies,
            JurikMovingAverageStrategies,
            KDJIndicatorStrategies,
            KaufmanAdaptiveMovingAverageStrategies,
            KaufmanEfficiencyIndicatorStrategies,
            KeltnerChannelStrategies,
            KlingerVolumeOscillatorStrategies,
            KnowSureThingStrategies,
            LinearRegressionStrategies,
            LinearRegressionAngleStrategies,
            LinearRegressionInterceptStrategies,
            LinearRegressionSlopeStrategies,
            LongRunStrategies,
            MarkWhistlersWAVEPMStrategies,
            MarketMomentumStrategies,
            MassIndexStrategies,
            McGinleyDynamicStrategies,
            MedianPriceStrategies,
            MidPointOverPeriodStrategies,
            MidpointPricePeriodStrategies,
            MinusDirectionalIndicatorStrategies,
            MinusDirectionalMovementStrategies,
            MoneyFlowIndexStrategies,
            MomentumStrategies,
            MomentumBreakoutBandsStrategies,
            MACDStrategies,
            MovingStandardDeviationStrategies,
            NegativeVolumeIndexStrategies,
            NormalizedAverageTrueRangeStrategies,
            NormalizedBASPStrategies,
            OnBalanceVolumeStrategies,
            OHLC_AverageStrategies,
            ParabolicStopAndReverseStrategies,
            PascalsWeightedMovingAverageStrategies,
            # PearsonsCorrelationCoefficientStrategies,
            PercentBStrategies,
            PercentagePriceOscillatorStrategies,
            PercentageVolumeOscillatorStrategies,
            PivotPointsStrategies,
            PlusDirectionalIndicatorStrategies,
            PlusDirectionalMovementStrategies,
            PositiveVolumeIndexStrategies,
            PrettyGoodOscillatorStrategies,
            PriceDistanceStrategies,
            PriceVolumeRankStrategies,
            PriceVolumeTrendStrategies,
            PriceVolumeStrategies,
            PsychologicalLineStrategies,
            QStickStrategies,
            QuantitativeQualitativeEstimationStrategies,
            RateOfChangeStrategies,
            RelativeStrengthIndexStrategies,
            RelativeStrengthXtraStrategies,
            RelativeVigorIndexStrategies,
            RelativeVolatilityIndexStrategies,
            SchaffTrendCycleStrategies,
            ShortRunStrategies,
            SineWeightedMovingAverageStrategies,
            SlopeStrategies,
            SmiErgodicOscillatorStrategies,
            SmoothedExponentialMovingAverageStrategies,
            SmoothedSimpleMovingAverageStrategies,
            SqueezeStrategies,
            SqueezeProStrategies,
            StandardDeviationStrategies,
            StochasticStrategies,
            StochasticFastStrategies,
            StochasticOscillatorStrategies,
            StochasticOscillatorKStrategies,
            StochasticRSIStrategies,
            StochasticOscillatorDStrategies,
            StopAndReverseStrategies,
            SummationStrategies,
            SupertrendStrategies,
            SymmetricWeightedMovingAverageStrategies,
            T3MovingAverageStrategies,
            TDSequentialStrategies,
            TrendSignalsStrategies,
            TriangularMovingAverageStrategies,
            TripleExponentialMovingAverageOscillatorStrategies,
            TrixStrategies,
            TwiggsMoneyIndexStrategies,
            TTMTrendStrategies,
            TypicalPriceStrategies,
            UltimateOscillatorStrategies,
            UlcerIndexStrategies,
            UpDownStrategies,
            VariableIndexDynamicAverageStrategies,
            VarianceStrategies,
            VerticalHorizontalFilterStrategies,
            VolumeAdjustedMovingAverageStrategies,
            VolumeFlowIndicatorStrategies,
            VolumePriceTrendStrategies,
            # VolumeProfileStrategies,
            VolumeWeightedAveragePriceStrategies,
            VolumeWeightedMovingAverageStrategies,
            # VolumeZoneOscillatorStrategies,
            VolumeWeightedMACDStrategies,
            VortexIndicatorStrategies,
            WaveTrendOscillatorStrategies,
            WeightedClosingPriceStrategies,
            WeightedMovingAverageStrategies,
            WeightedOnBalanceVolumeStrategies,
            WilliamsRStrategies,
            WildersMovingAverageStrategies,
            ZeroLagExponentialMovingAverageStrategies,
            ZeroLagSimpleMovingAverageStrategies,
        ]
        # Instantiate each strategy and store the instance in a dictionary
        self.strategy_instances = {}
        for cls in strategy_classes:
            self.strategy_instances[cls.__name__] = cls()

    def run_all_strategies(self, df, append=True, ta_indicator_value=False, 
                           signal_score=True, signal_value=False, signal_explanation=False):
        """
        Runs the `run_all_strategies` method for each strategy instance using the provided DataFrame.
        The results are stored as attributes (e.g., AberrationStrategies_df) and then concatenated
        horizontally into one DataFrame.
        """
        results = {}
        for name, instance in self.strategy_instances.items():
            # Run the strategy and store the result DataFrame
            result_df = instance.run_all_strategies(
                df,
                append=append,
                ta_indicator_value=ta_indicator_value,
                signal_score=signal_score,
                signal_value=signal_value,
                signal_explanation=signal_explanation
            )
            results[name] = result_df
            setattr(self, f"{name}_df", result_df)
        
        # Concatenate all the result DataFrames side-by-side
        return pd.concat(results.values(), axis=1)