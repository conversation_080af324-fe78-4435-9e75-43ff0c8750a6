from dotenv import load_dotenv

load_dotenv()
import json
import os
import re
from pprint import pprint as p
from typing import List, Union

from langchain import hub
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.tools.retriever import create_retriever_tool
from langchain_community.vectorstores import FAISS
from langchain_core.prompts import (
    HumanMessagePromptTemplate,
    MessagesPlaceholder,
    PromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain_openai import ChatOpenAI, OpenAIEmbeddings


class FeatureMapper:
    def __init__(self, mapper_file):
        with open(mapper_file, "r") as f:
            self.mapper = json.load(f)

    def map_feature(self, word):
        try:
            word = word.lower()
            return self.mapper.get(word, None)
        except Exception as e:
            print(e)


class MetricSearch:
    def __init__(self):
        self.mapper_file = os.path.join(os.getcwd(),"app/tools/internal_search/quantitative_analysis_tool/parameters", "table_mapper.json")

    def semantic_search(self, user_ask):
        try:
            final_process = []
            vector_index_path = os.path.join(
                os.getcwd(), "app/tools/internal_search/quantitative_analysis_tool/parameters", "faiss_index_latest"
            )

            embeddings = OpenAIEmbeddings()
            saved_vector_store = FAISS.load_local(
                vector_index_path, embeddings, allow_dangerous_deserialization=True
            )
            searched_metric = saved_vector_store.similarity_search_with_relevance_scores(
                user_ask, k=10
            )
            FM = FeatureMapper(mapper_file=self.mapper_file)
            for doc, score in searched_metric:
                try:
                    col_name = doc.metadata.get(
                        "column_name", None
                    )  # Use None or any default value if 'column_name' is missing
                    tab_name = doc.metadata.get("table_name", None)

                    if col_name and tab_name:
                        finalized_data = {tab_name: [col_name]}
                        final_process.append(finalized_data)
                    elif tab_name:
                        tab = FM.map_feature(word=tab_name)
                        final_process.append(tab)
                except Exception as e:
                    pass
        except Exception as e:
            print(e)
        return final_process

    def rag_agent_prompt(self):

        # Define the main prompt structure
        chat_history_placeholder = MessagesPlaceholder(
            variable_name="chat_history", optional=True
        )
        user_message_template = HumanMessagePromptTemplate(
            prompt=PromptTemplate(input_variables=["input"], template="{input}")
        )
        agent_scratchpad_placeholder = MessagesPlaceholder(
            variable_name="agent_scratchpad"
        )

        # System message introducing the agent
        intro_prompt = SystemMessagePromptTemplate(
            prompt=PromptTemplate(
                input_variables=[],
                template="You are a helpful assistant.Searches and returns the best and closet financial metric using user provided input.You have to return the key only.As it is the part of the automated pipeline. Don't provide explanation. only list of values. no extra text is required. Expected output (k1,k2)",
            )
        )

        # Combine all elements into the final prompt
        final_prompt = (
            intro_prompt
            + chat_history_placeholder
            + user_message_template
            + agent_scratchpad_placeholder
        )
        return final_prompt

    def find_table_and_column(self, incoming_word):
        file_path = os.path.join(os.getcwd(),"app/tools/internal_search/quantitative_analysis_tool/FIASS_Store", "latest_output.json")
        # Read and parse the JSON file
        with open(file_path, "r") as file:
            data = json.load(file)

        for item in data:
            table = item.get("table", {})
            table_name = table.get("name")
            columns = table.get("columns", [])
            if incoming_word == table_name:
                return {"table": table_name, "column": None}

            for column in columns:
                column_name = column.get("name")
                if column_name == incoming_word:
                    return {"table": table_name, "column": column_name}

        return "No match found"

    def semantic_search_agent(self, user_ask):
        try:
            print("what i got", user_ask)
            vector_index_path = os.path.join(
                os.getcwd(),"app/tools/internal_search/quantitative_analysis_tool/parameters", "faiss_index_latest"
            )
            
            embeddings = OpenAIEmbeddings()
            db = FAISS.load_local(
                vector_index_path, embeddings, allow_dangerous_deserialization=True
            )
            retriever = db.as_retriever()
            tool = create_retriever_tool(
                retriever,
                "financial_metric_retriever",
                "Searches and returns the best and closet financial metric using user provided input.Provided input should not have company name or ticker. You have to return the key only.As it is the part of the automated pipeline. no extra text is required",
            )
            tools = [tool]

            # prompt = hub.pull("hwchase17/openai-tools-agent")
            # print(prompt)
            # p(prompt.messages)

            prompt = self.rag_agent_prompt()

            llm = ChatOpenAI(temperature=0)

            agent = create_openai_tools_agent(llm, tools, prompt)
            agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)
            system_prompt = "User question: "
            final_prompt = system_prompt + user_ask
            result = agent_executor.invoke({"input": final_prompt})

            pattern = r"\b\w*_\w*\b"
            matches = re.findall(pattern, result["output"])
            # Test the function
            print(type(matches), matches)
            final_process = []
            FM = FeatureMapper(mapper_file=self.mapper_file)

            for doc in matches:
                try:
                    result = self.find_table_and_column(doc)
                    print(result)
                    if result["column"] and result["table"]:
                        finalized_data = {result["table"]: [result["column"]]}
                        final_process.append(finalized_data)
                    elif result["table"]:
                        tab = FM.map_feature(word=result["table"])
                        final_process.append(tab)
                except Exception as e:
                    pass
            return final_process
        except Exception as e:
            print(e)

