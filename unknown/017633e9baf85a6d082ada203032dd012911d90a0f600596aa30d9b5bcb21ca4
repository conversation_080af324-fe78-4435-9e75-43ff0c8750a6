from autogen_core.tools import FunctionTool

from app.tools.internal_search.helper import (
    qualitative_analysis_tool,
    quantitative_analysis_tool,
)

# Query Retrieval tool to retrieve quantitative data
quantitative_analysis_tool = FunctionTool(
    func=quantitative_analysis_tool,
    name="quantitative_analysis_tool",
    description="""A tool to retrieve financial data for a specified user_query, asset, metric, start date, end date,. If the parameter - financial_metric - is not provided, ask for it. Break the query so each one is completely different and represent only one metric.""",
)

# Query Retrieval tool to retrieve qualitative data
qualitative_analysis_tool = FunctionTool(
    func=qualitative_analysis_tool,
    name="qualitative_analysis_tool",
    description="""A tool designed to Analyze qualitative data for a specified asset within a defined date range(Take the latest year 2024 if not specified). Start by generating multiple varied augmented queries that rephrase or expand on the original input, maintaining core intent and offering diverse insights. Then, analyze the relevant data based on these queries and provide a detailed response.""",
)
