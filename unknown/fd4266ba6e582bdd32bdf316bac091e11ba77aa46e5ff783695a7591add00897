from datetime import datetime, timedelta
from typing import Annotated, Any, Dict, List

from app.tools.internal_search import agent_tool_schema
from .quantitative_analysis_tool import quantitative
from .qualitative_analysis_tool import qualitative
from app.logging_custom_directory.logger_custom import logger
from app import (
    CUSTOM_STOP_WORDS,
    alphavantage_api,
    db_conventional_string,
    json_mapper_path,
    vector_file_path,
)

def quantitative_analysis_tool(
    extracted_input: Annotated[
        agent_tool_schema.QuantitativeDataInput,
        "Input parameters for retrieving financial data",
    ],
) -> str:
    """
    Retrieves and processes quantitative financial data based on user input.

    This tool takes structured input specifying the desired financial data,
    including the asset, date range, financial metrics, and chart type.
    It utilizes a `QuanitativeTool` to fetch the data from a specified
    source (e.g., Alpha Vantage, database) and returns the result as a string.

    Args:
        extracted_input (agent_tool_schema.QuantitativeDataInput): An object
            containing the parameters for the quantitative data retrieval.
            This object is expected to have attributes such as:
            - user_query (Optional[str]): The user's natural language query.
            - ticker (Optional[str]): The ticker symbol of the asset.
            - start_date (Optional[str]): The start date for the data (YYYY-MM-DD).
            - end_date (Optional[str]): The end date for the data (YYYY-MM-DD).
            - asset_name (Optional[str]): The name of the asset.
            - financial_metric (Optional[str]): The specific financial metric to retrieve.
            - chart_type (Optional[str]): The desired type of chart for visualization.

    Returns:
        str: A string representation of the fetched and potentially processed
            quantitative financial data. Returns an error message if the
            user query is missing.

    Raises:
        Exception: If any error occurs during the data fetching or processing,
            the error is logged. The function currently returns the string
            representation of the data even if an error occurred within the
            `QuanitativeTool`.

    Example:
        >>> # Assuming extracted_input is an instance of QuantitativeDataInput
        >>> extracted_input.user_query = "Get the historical stock price of AAPL"
        >>> extracted_input.ticker = "AAPL"
        >>> extracted_input.start_date = "2024-01-01"
        >>> extracted_input.end_date = "2024-01-31"
        >>> result = quantitative_analysis_tool(extracted_input)
        >>> print(result)
        '[{"date": "2024-01-31", "close": 170.34}, {"date": "2024-01-30", "close": 169.58}, ...]'
    """    
    try:

        qunatitative_tool = quantitative.QuanitativeTool(
        alphavantage_api=alphavantage_api,
        connection_string=db_conventional_string,
        stop_words=CUSTOM_STOP_WORDS,
        vector_file_path=vector_file_path,
        mapper_file=json_mapper_path,
        )
        """Analyze quantitative query from RPC"""
        if extracted_input.user_query == None:
            text = (
                "Can't retrive data. User query is important. Kindly provide me user query"
            )
            return text
        logger.info(extracted_input.user_query)
        ticker=""
        if extracted_input.ticker:
            ticker = extracted_input.ticker


        if extracted_input.start_date is None and extracted_input.end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
            extracted_input.start_date = start_date
            extracted_input.end_date = end_date

        # If start date not provided
        elif extracted_input.start_date is None:
            start_date = (
                datetime.strptime(extracted_input.end_date, "%Y-%m-%d")
                - timedelta(days=365)
            ).strftime("%Y-%m-%d")
            extracted_input.start_date = start_date

        # If end date not provided
        elif extracted_input.end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            extracted_input.end_date = end_date

        asset_name = extracted_input.asset_name
        # return data
        data = qunatitative_tool.fetch_data(
            ticker,
            asset_name,
            extracted_input.user_query,
            extracted_input.financial_metric,
            extracted_input.start_date,
            extracted_input.end_date,
            extracted_input.chart_type,
        )
        return str(data)
    except Exception as e:
        logger.error(e)


def qualitative_analysis_tool(
    extracted_input: Annotated[
        agent_tool_schema.Qualitative,
        f"This dictionary includes: 'ticker' for the stock symbol (e.g., AAPL), 'start_date' and 'end_date' based on the query with default values of one year to the {datetime.now().year}) and the {datetime.now().date()}) respectively if unspecified. For specific periods like 'two quarters' or 'this year,' the dates reflect the current year (e.g., January 1st and June 30th for the first two quarters of {datetime.now().year})). 'augmented_user_query' to generate multiple varied queries exploring different aspects of the topic, and 'filling_type' to determine the appropriate report type (10-K, 10-Q, or 8-K) based on the user’s request. for quaterly 10-Q, for anually 10-K and 8-K for current information. if filling type not specified use 'ALL' both types of filling.",
    ],
) -> str:
    """
    Performs qualitative analysis by searching through financial filings
    (10-K, 10-Q, 8-K) based on user queries and specified parameters.

    This tool takes structured input including the stock ticker, a date range
    to filter filings, a list of augmented user queries to broaden the search,
    and the specific type of filing to look for. It then utilizes a
    `qualitative` module to perform the search and retrieve relevant information.

    Args:
        extracted_input (agent_tool_schema.Qualitative): An object containing
            the parameters for the qualitative analysis. This object is expected
            to have attributes such as:
            - ticker (str): The stock ticker symbol (e.g., AAPL).
            - start_date (str): The start date for filtering filings (YYYY-MM-DD).
              Defaults to one year ago from today.
            - end_date (str): The end date for filtering filings (YYYY-MM-DD).
              Defaults to today.
            - augmented_user_query (List[str]): A list of rephrased or expanded
              user queries to explore different aspects of the topic.
            - filling_type (str): The type of financial filing to search for
              ('10-K', '10-Q', '8-K', or 'ALL'). Defaults to 'ALL'.

    Returns:
        str: The result of the qualitative search, which could be a string
            containing relevant excerpts from the filings or a summary based
            on the queries. The exact format depends on the implementation
            of the `qualitative_searching` method.

    Raises:
        Exception: If any error occurs during the qualitative searching process
            within the `qualitative` module, it might be logged. The function
            currently returns the result of the search regardless of potential
            internal errors in the `qualitative_searching` method.

    Example:
        >>> # Assuming extracted_input is an instance of agent_tool_schema.Qualitative
        >>> extracted_input.ticker = "GOOGL"
        >>> extracted_input.augmented_user_query = ["What are the key risks?", "Discuss future growth strategies."]
        >>> extracted_input.filling_type = "10-K"
        >>> result = qualitative_analysis_tool(extracted_input)
        >>> print(result)
        'Based on the 10-K filings for GOOGL, key risks include...'
    """    
    # for tickers
    logger.info(extracted_input.ticker)
    logger.info(extracted_input.start_date)
    logger.info(extracted_input.end_date)
    logger.info(extracted_input.augmented_user_query)
    logger.info(extracted_input.filling_type)

    print(extracted_input.start_date)
    print(extracted_input.end_date)
    
    qual_ins = qualitative.qualitative()

    result = qual_ins.qualitative_searching(
        user_queries=extracted_input.augmented_user_query,
        ticker=extracted_input.ticker,
        start_date=extracted_input.start_date,
        end_date=extracted_input.end_date,
        filling_type=extracted_input.filling_type,
    )
    logger.info(result)
    # result = "GO HOME."
    return result
