.
├── DockerFile
├── README.md
├── app
│   ├── __init__.py
│   ├── agent
│   │   ├── agent_creation.py
│   │   └── calling_agent.py
│   ├── api
│   │   ├── __init__.py
│   │   ├── api.py
│   │   ├── api_payload_schema.py
│   │   └── generate_reply.py
│   ├── core
│   │   ├── __init__.py
│   │   └── config.py
│   ├── db
│   │   ├── __init__.py
│   │   └── session.py
│   ├── logging_custom_directory
│   │   ├── __init__.py
│   │   └── logger_custom.py
│   ├── main.py
│   ├── schemas
│   │   └── __init__.py
│   ├── services
│   │   └── __init__.py
│   └── tools
│       ├── internal_search
│       ├── web_search
│       └── workflows
├── logs
│   ├── 2025-04-05
│   └── 2025-04-06
├── requirements.txt
├── run.py
└── structure.txt

16 directories, 29 files
