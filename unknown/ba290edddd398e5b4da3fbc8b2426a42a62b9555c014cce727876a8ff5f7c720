import ast

from app.db.db import SQLTool
from app.logging_custom_directory.logger_custom import logger


class Orchistration:
    def __init__(self):
        pass

    def Displayable_data(self, conversation):
        try:
            displayable_rag_data = {}
            # print(conversation)
            if hasattr(conversation, "messages"):
                messages = conversation.messages
            elif (
                isinstance(conversation, dict) and "messages" in conversation
            ):
                messages = conversation["messages"]
            else:
                logger.error("No messages found in conversation.")
                return {}

            for msg in messages:
                source = getattr(msg, "name", None) or (
                    msg.get("name") if isinstance(msg, dict) else None
                )

                if not source:
                    continue  # skip this message if no source found

                if source == "work_flows":
                    if "Work Flows" not in displayable_rag_data:
                        displayable_rag_data["Work Flows"] = []

                    # Case 1: content is a string with data
                    if isinstance(msg.content, str) and msg.content.strip():
                        displayable_rag_data["Work Flows"].append(msg.content)
                        continue

                    # Case 2: content is a list, and first item has a .content attribute
                    elif isinstance(msg.content, list) and msg.content:
                        first = msg.content[0]
                        if hasattr(first, "content"):
                            displayable_rag_data["Work Flows"].append(msg.content)
                            continue

                if source == "web_researcher":
                    if "Web Researcher" not in displayable_rag_data:
                        displayable_rag_data["Web Researcher"] = []

                    # Case 1: content is a string with data
                    if isinstance(msg.content, str) and msg.content.strip():
                        displayable_rag_data["Web Researcher"].append(msg.content)
                        continue

                    # Case 2: content is a list, and first item has a .content attribute
                    elif isinstance(msg.content, list) and msg.content:
                        first = msg.content[0]
                        if hasattr(first, "content"):
                            displayable_rag_data["Web Researcher"].append(msg.content)
                            continue

                if source == "internal_search":
                    if "Internal Search" not in displayable_rag_data:
                        displayable_rag_data["Internal Search"] = []

                    # Case 1: content is a string with data
                    if isinstance(msg.content, str) and msg.content.strip():
                        displayable_rag_data["Internal Search"].append(msg.content)
                        continue

                    # Case 2: content is a list, and first item has a .content attribute
                    elif isinstance(msg.content, list) and msg.content:
                        first = msg.content[0]
                        if hasattr(first, "content"):
                            displayable_rag_data["Internal Search"].append(msg.content)
                            continue


            return displayable_rag_data
        except Exception as e:
            logger.error(f"displayable_rag_data : Error: {e} ")

    def Displayable_data_v2(self, conversation):
        try:
            displayable_rag_data = {}
            if "output_from_workflow" in conversation:
                displayable_rag_data["Work Flows"] = []
                displayable_rag_data["Work Flows"].append(conversation["output_from_workflow"])


            if "output_from_web" in conversation:
                displayable_rag_data["Web Researcher"] = []
                displayable_rag_data["Web Researcher"].append(conversation["output_from_web"])

            if "output_from_internal" in conversation:
                displayable_rag_data["Internal Search"] = []
                displayable_rag_data["Internal Search"].append(conversation["output_from_internal"])

            return displayable_rag_data
        except Exception as e:
            logger.error(f"displayable_rag_data : Error: {e} ")

    def token_count(self, conversation):
        try:
            if hasattr(conversation, "messages"):
                messages = conversation.messages
            elif (
                isinstance(conversation, dict) and "messages" in conversation
            ):
                messages = conversation["messages"]
            else:
                logger.error("No messages found in conversation.")
                return {}
            for msg in messages:
                if msg.models_usage is not None:
                    prompt_tokens = msg.models_usage.prompt_tokens
                    completion_tokens = msg.models_usage.completion_tokens
                    break

            return {
                "Prompt Tokens": prompt_tokens,
                "Completion Tokens": completion_tokens,
            }
        except Exception as e:
            logger.error(f"token_count : Error: {e} ")

    def token_count_v2(self, conversation):
        try:
            prompt_tokens =0
            completion_tokens =0
            if hasattr(conversation, "messages"):
                messages = conversation.messages
            elif (
                isinstance(conversation, dict) and "messages" in conversation
            ):
                messages = conversation["messages"]
            else:
                logger.error("No messages found in conversation.")
                return {}
            for msg in messages:
                if msg.model_extra is not None and msg.model_extra.get('usage_metadata') is not None:

                    prompt_tokens += msg.model_extra['usage_metadata']['input_tokens']
                    completion_tokens += msg.model_extra['usage_metadata']['total_tokens']
                    continue

            return {
                "Prompt Tokens": prompt_tokens,
                "Completion Tokens": completion_tokens,
            }
        except Exception as e:
            logger.error(f"token_count : Error: {e} ")


    def history_conversation(self, user_id, symbol, SqlTool, chat_id=None):
        try:
            history = SqlTool.update_read_data_testing(symbol=symbol, user_id=user_id, chat_id=chat_id)
            print(history)

            previous_history = []

            # Get only the last 10 messages (or fewer if history is shorter)
            # This will include the last 5 complete exchanges (question-answer pairs)
            message_count = min(6, len(history[0]))
            last_messages = history[0][-message_count:]

            # Process the limited messages
            # If we have an odd number of messages, handle it gracefully
            for i in range(0, len(last_messages), 2):
                if i+1 < len(last_messages):
                    question = last_messages[i]["content"]
                    answer = last_messages[i+1]["content"]
                    previous_history.append([question, answer])
                else:
                    # Handle case where the last message is a question without an answer
                    question = last_messages[i]["content"]
                    previous_history.append([question, ""])

            return previous_history

        except Exception as e:
            logger.error(f"history_conversation : Error: {e} ")
            return []
