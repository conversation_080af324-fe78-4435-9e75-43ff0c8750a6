# Memory Management API Documentation

## Overview

This document describes the new memory management system implemented for the TraderGPT application. The system provides full CRUD (Create, Read, Update, Delete) operations for long-term memories using the mem0 memory system.

## What's New

### 1. Enhanced Memory Control
- **New Parameter**: `longterm` - Controls both searching and storing of long-term memories (mem0)
- **Memory Management**: Full API endpoints for managing user memories
- **User Isolation**: Each user can only access and manage their own memories

### 2. New API Endpoints
Four new endpoints have been added for comprehensive memory management:
- `GET /api/memories` - Retrieve user memories
- `DELETE /api/memories/{memory_id}` - Delete specific memory
- `DELETE /api/memories` - Delete all user memories  
- `GET /api/memories/count` - Get memory count

---

## API Endpoints Documentation

### Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### 1. Get User Memories
**Endpoint**: `GET /api/memories`

**Description**: Retrieve all long-term memories for the authenticated user

**Query Parameters**:
- `limit` (optional): Maximum number of memories to retrieve
  - Default: 100
  - Maximum: 500
  - Minimum: 1

**Response**:
```json
{
  "user_id": "user123",
  "total_memories": 15,
  "limit": 100,
  "memories": [
    {
      "id": "mem_123abc",
      "memory": "User prefers technical analysis over fundamental analysis for AAPL",
      "metadata": {
        "user_id": "user123",
        "source": "tradergpt",
        "memory_type": "conversation",
        "chat_id": "chat_456"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "source": "mem0"
    }
  ]
}
```

**Example**:
```bash
curl -X GET "http://localhost:8000/api/memories?limit=50" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 2. Delete Specific Memory
**Endpoint**: `DELETE /api/memories/{memory_id}`

**Description**: Delete a specific memory by its ID

**Path Parameters**:
- `memory_id`: The unique ID of the memory to delete

**Response**:
```json
{
  "message": "Memory mem_123abc deleted successfully",
  "memory_id": "mem_123abc",
  "user_id": "user123"
}
```

**Example**:
```bash
curl -X DELETE "http://localhost:8000/api/memories/mem_123abc" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 3. Delete All User Memories
**Endpoint**: `DELETE /api/memories`

**Description**: Delete ALL memories for the authenticated user (use with caution!)

**Response**:
```json
{
  "message": "All memories deleted successfully for user user123",
  "user_id": "user123"
}
```

**Example**:
```bash
curl -X DELETE "http://localhost:8000/api/memories" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 4. Get Memory Count
**Endpoint**: `GET /api/memories/count`

**Description**: Get the total count of memories for the authenticated user

**Response**:
```json
{
  "user_id": "user123",
  "total_memories": 42
}
```

**Example**:
```bash
curl -X GET "http://localhost:8000/api/memories/count" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

---

## Enhanced Conversation API

### New Parameter: `longterm`

Both conversation endpoints now support the `longterm` parameter that controls both searching and storing:

#### Modified Endpoints:
- `POST /api/user_ask`
- `POST /api/user_ask_stream`

#### New Request Schema:
```json
{
  "task": "What's the latest on Apple?",
  "symbol": "AAPL",
  "chat_id": "chat_456", 
  "report": false,
  "deep_search": false,
  "longterm": true  // NEW PARAMETER
}
```

#### Parameter Details:
- **Type**: `boolean`
- **Default**: `true`
- **Description**: Whether to use long-term memory (mem0) for both searching and storing
- **When `true`**: 
  - System searches user's long-term memories for relevant context
  - Conversation is stored in both mem0 and existing memory systems
- **When `false`**: 
  - System skips mem0 memory search (faster responses but no historical context)
  - Conversation is stored ONLY in existing memory system (not in mem0)

#### Examples:

**With Long-term Memory (default)**:
```bash
curl -X POST "http://localhost:8000/api/user_ask" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "What do you think about AAPL?",
    "symbol": "AAPL",
    "longterm": true
  }'
```

**Without Long-term Memory**:
```bash
curl -X POST "http://localhost:8000/api/user_ask" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "What do you think about AAPL?",
    "symbol": "AAPL", 
    "longterm": false
  }'
```

---

## Memory Storage Behavior

### Storage Logic Based on `longterm` Parameter:

| `longterm` Value | Search Behavior | Storage Behavior |
|------------------|-----------------|------------------|
| `true` (default) | ✅ Searches mem0 memories for context | ✅ Stores in both mem0 AND existing memory system |
| `false` | ❌ Skips mem0 search (faster response) | ✅ Stores ONLY in existing memory system |

### Benefits of Different Modes:

#### **`longterm: true` (Recommended for most use cases)**
- **Pros**: 
  - Rich historical context from previous conversations
  - Personalized responses based on user preferences
  - Long-term learning and adaptation
- **Cons**: 
  - Slightly slower response due to memory search
  - Uses more storage space

#### **`longterm: false` (For privacy-focused or temporary sessions)**
- **Pros**: 
  - Faster response times
  - More privacy (no long-term storage in mem0)
  - Useful for temporary/guest sessions
- **Cons**: 
  - No historical context
  - Responses may be less personalized

---

## Error Handling

### Common Error Responses:

#### Authentication Errors:
```json
{
  "error": "token expired"
}
```

#### System Unavailable:
```json
{
  "error": "Memory system (mem0) is not available"
}
```

#### Validation Errors:
```json
{
  "error": "memory_id is required"
}
```

#### Operation Failures:
```json
{
  "error": "Failed to delete memory mem_123abc"
}
```

---

## Frontend Integration Guide

### 1. Memory Management Dashboard

Create a dashboard to display and manage user memories:

```javascript
// Fetch user memories
async function getUserMemories(limit = 100) {
  const response = await fetch(`/api/memories?limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  return await response.json();
}

// Delete specific memory
async function deleteMemory(memoryId) {
  const response = await fetch(`/api/memories/${memoryId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  return await response.json();
}

// Get memory count for pagination
async function getMemoryCount() {
  const response = await fetch('/api/memories/count', {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  return await response.json();
}
```

### 2. Memory Toggle in Chat Interface

Add a toggle to control long-term memory usage:

```javascript
// Chat with memory control
async function sendMessage(message, symbol, useLongterm = true) {
  const response = await fetch('/api/user_ask', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      task: message,
      symbol: symbol,
      longterm: useLongterm,
      report: false,
      deep_search: false
    })
  });
  return await response.json();
}
```

### 3. Memory Management Components

**React Example**:
```jsx
import React, { useState, useEffect } from 'react';

const MemoryManager = () => {
  const [memories, setMemories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [longtermEnabled, setLongtermEnabled] = useState(true);

  const loadMemories = async () => {
    setLoading(true);
    const data = await getUserMemories();
    setMemories(data.memories || []);
    setLoading(false);
  };

  const handleDeleteMemory = async (memoryId) => {
    if (confirm('Are you sure you want to delete this memory?')) {
      await deleteMemory(memoryId);
      loadMemories(); // Refresh list
    }
  };

  const handleSendMessage = async (message, symbol) => {
    return await sendMessage(message, symbol, longtermEnabled);
  };

  useEffect(() => {
    loadMemories();
  }, []);

  return (
    <div className="memory-manager">
      <div className="memory-settings">
        <label>
          <input
            type="checkbox"
            checked={longtermEnabled}
            onChange={(e) => setLongtermEnabled(e.target.checked)}
          />
          Enable Long-term Memory
        </label>
        <small>
          {longtermEnabled 
            ? "Conversations will be stored and searched in long-term memory" 
            : "Conversations will only be stored in session memory"
          }
        </small>
      </div>
      
      <h2>Your Long-term Memories</h2>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="memory-list">
          {memories.map(memory => (
            <div key={memory.id} className="memory-item">
              <p>{memory.memory}</p>
              <small>Created: {memory.created_at}</small>
              <button onClick={() => handleDeleteMemory(memory.id)}>
                Delete
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

---

## Security Features

### 1. Authentication Required
- All endpoints require valid JWT authentication
- Tokens are validated on every request

### 2. User Isolation
- Users can only access their own memories
- Memory operations are scoped to the authenticated user
- No cross-user data access possible

### 3. Input Validation
- Memory ID validation for delete operations
- Limit validation for memory retrieval
- Proper error handling for invalid requests

### 4. Availability Checks
- System verifies mem0 availability before operations
- Graceful degradation when memory system is unavailable

---

## Technical Implementation Details

### Files Modified/Created:

1. **`app/memory/mem0_helper.py`**:
   - Added `get_all_memories()` method
   - Added `delete_memory()` method  
   - Added `delete_all_memories()` method
   - Added convenience functions

2. **`app/api/api_payload_schema.py`**:
   - Added `longterm` parameter to `UserAskRequest`
   - Added `DeleteMemoryRequest` schema
   - Added `GetMemoriesRequest` schema

3. **`app/api/generate_reply.py`**:
   - Updated conversation endpoints to use `longterm`
   - Added 4 new memory management endpoints
   - Added proper error handling and logging

4. **`app/agent/calling_agent.py`**:
   - Updated `generating_reply()` function with `longterm` parameter
   - Updated `ReplyStream.generate()` method with `longterm` parameter
   - Added conditional memory search and storage logic

5. **`app/services/background_tasks.py`**:
   - Uses existing `task_type` parameter to control storage behavior
   - Supports "mem0", "existing", or "both" storage modes

### Memory Data Structure:
```python
{
    "id": "unique_memory_id",
    "memory": "memory_content_text", 
    "metadata": {
        "user_id": "user123",
        "source": "tradergpt",
        "memory_type": "conversation",
        "chat_id": "chat_456"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "source": "mem0"
}
```

---

## Testing the Implementation

### 1. Test Memory Creation and Storage Behavior
1. **Test with `longterm: true`**:
   - Have a conversation with the system
   - Check if memories are created in both systems
   - Use `GET /api/memories` to verify mem0 storage
   
2. **Test with `longterm: false`**:
   - Have a conversation with the system
   - Verify memories are NOT stored in mem0
   - Check that only existing memory system has the conversation

### 2. Test Memory Retrieval
1. Send requests with `longterm: true`
2. Verify relevant memories are included in responses
3. Test with `longterm: false` for comparison

### 3. Test Memory Deletion
1. Get memory list
2. Delete specific memories
3. Verify they're removed from subsequent requests

### 4. Test Error Handling
1. Try accessing without authentication
2. Try deleting non-existent memories
3. Test with invalid parameters

---

## Best Practices

### For Frontend Developers:
1. **Always handle authentication errors** - Redirect to login when token expires
2. **Implement confirmation dialogs** for delete operations
3. **Show loading states** during memory operations
4. **Paginate memory lists** for better performance
5. **Cache memory count** to avoid frequent API calls
6. **Provide clear UI indicators** for longterm memory state

### For Backend Developers:
1. **Always validate user permissions** before memory operations
2. **Log all memory operations** for debugging
3. **Handle mem0 unavailability gracefully**
4. **Use appropriate HTTP status codes**
5. **Implement rate limiting** for memory endpoints

### For System Administrators:
1. **Monitor mem0 system health**
2. **Set up proper backup strategies** for memory data
3. **Configure appropriate limits** for memory retrieval
4. **Monitor API usage patterns**
5. **Track storage usage** in both memory systems

---

## Use Case Scenarios

### 1. **Regular User Session** (`longterm: true`)
- User wants personalized experience
- System remembers preferences and conversation history
- Best for logged-in users with accounts

### 2. **Privacy Mode** (`longterm: false`)
- User wants privacy-focused interaction
- No long-term memory storage
- Suitable for sensitive topics or guest users

### 3. **Performance Mode** (`longterm: false`)
- When speed is prioritized over personalization
- Useful for high-frequency API calls
- Good for automated systems or bots

### 4. **Demo/Testing Mode** (`longterm: false`)
- For demonstrations or testing
- Prevents pollution of memory with test data
- Easy cleanup since no long-term storage

---

## Troubleshooting

### Common Issues:

1. **"Memory system (mem0) is not available"**
   - Check if mem0 service is running
   - Verify Qdrant connection
   - Check mem0 configuration

2. **"token expired"**
   - User needs to re-authenticate
   - Check JWT token validation

3. **Inconsistent memory behavior**
   - Check `longterm` parameter value
   - Verify task_type in background storage logs
   - Check if mem0 is available

4. **Memory deletion fails**
   - Verify memory ID exists
   - Check user permissions
   - Check mem0 service status

---

## Migration Guide

### From Previous Version:

If you were using the old `search_longterm` parameter:

#### **Old API Call**:
```json
{
  "task": "What's Apple's outlook?",
  "symbol": "AAPL",
  "search_longterm": true
}
```

#### **New API Call**:
```json
{
  "task": "What's Apple's outlook?", 
  "symbol": "AAPL",
  "longterm": true
}
```

#### **Key Changes**:
1. Parameter renamed from `search_longterm` to `longterm`
2. Now controls both search AND storage behavior
3. `longterm: false` prevents mem0 storage (new feature)

---

## Future Enhancements

### Potential Improvements:
1. **Memory Search**: Add text search within memories
2. **Memory Categories**: Categorize memories by topic/symbol
3. **Memory Export**: Allow users to export their memories
4. **Memory Analytics**: Show memory usage statistics
5. **Memory Sharing**: Allow selective sharing between users
6. **Memory Backup**: Automated backup and restore functionality
7. **Hybrid Storage**: Allow fine-grained control over storage systems
8. **Memory Compression**: Optimize storage for large conversation histories

---

## Support

For technical support or questions about this implementation:
1. Check the logs in `/logs/` directory
2. Verify system configuration
3. Test with minimal examples provided in this documentation
4. Contact the development team with specific error messages and steps to reproduce

---

**Last Updated**: January 2024  
**Version**: 2.0  
**Compatible with**: TraderGPT v2.0+ 