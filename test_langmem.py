"""
Test script for the LangMem memory system.
"""
import asyncio
import datetime
import json
import logging
import os
import sys

from langchain_openai import ChatOpenAI
from langmem import (
    create_memory_manager,
    create_memory_store_manager,
    create_prompt_optimizer,
    utils
)
from langmem.utils import NamespaceTemplate
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.http import models

from app.memory.memory_manager import search_memories, store_user_message
from app.memory.init_qdrant import init_qdrant_collection
from app.memory.langmem_config import (
    QDRANT_URL,
    QDRANT_COLLECTION_NAME,
    MEMORY_EXTRACTION_INSTRUCTIONS,
    MEMORY_NAMESPACE,
    LLM_MODEL,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_memory_system():
    """
    Test the LangMem memory system.
    """
    # Initialize Qdrant collection
    init_qdrant_collection()

    # Create test user and chat IDs
    user_id = "test_user_123"
    chat_id = f"{user_id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

    # Test storing a message
    logger.info("Testing storing a message...")
    messages = [
        {"role": "user", "content": "What do you think about Tesla stock?"},
        {"role": "assistant", "content": "Tesla (TSLA) is a leading electric vehicle manufacturer with significant growth potential but also faces increasing competition."}
    ]
    success = await store_user_message(
        user_id=user_id,
        chat_id=chat_id,
        messages=messages,
        run_async=False
    )
    logger.info(f"Stored message: {success}")

    # Wait a moment for the memory to be processed
    logger.info("Waiting for memory processing...")
    await asyncio.sleep(5)

    # Test searching memories
    logger.info("Testing searching memories...")
    memory_results = await search_memories(
        user_id=user_id,
        query="Tesla electric vehicles",
        limit=5,
        include_conversation_history=True,
        chat_id=chat_id
    )

    logger.info(f"Found {len(memory_results.get('results', []))} memories")
    for memory in memory_results.get("results", []):
        logger.info(f"Memory: {memory['memory']}")

    # Test storing another message
    logger.info("Testing storing another message...")
    messages = [
        {"role": "user", "content": "What about Apple stock?"},
        {"role": "assistant", "content": "Apple (AAPL) is a technology giant with a strong ecosystem and consistent performance."}
    ]
    success = await store_user_message(
        user_id=user_id,
        chat_id=chat_id,
        messages=messages,
        run_async=False
    )
    logger.info(f"Stored message: {success}")

    # Wait a moment for the memory to be processed
    logger.info("Waiting for memory processing...")
    await asyncio.sleep(5)

    # Test searching memories again
    logger.info("Testing searching memories again...")
    memory_results = await search_memories(
        user_id=user_id,
        query="Apple stock",
        limit=5,
        include_conversation_history=True,
        chat_id=chat_id
    )

    logger.info(f"Found {len(memory_results.get('results', []))} memories")
    for memory in memory_results.get("results", []):
        logger.info(f"Memory: {memory['memory']}")

    logger.info("Memory system tests completed successfully!")

if __name__ == "__main__":
    asyncio.run(test_memory_system())
