"""
Comprehensive test for both sliding window and time-weighted retrieval functionality.
"""
import asyncio
import datetime
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.memory.memory_manager import (
    store_user_message, search_memories, get_chat_memory_count, get_memory_store
)
from app.memory.init_qdrant import init_qdrant_collection
from app.memory.langmem_config import (
    MAX_MEMORIES_PER_CHAT, ENABLE_ORDER_RANKING, ORDER_WEIGHT_FACTOR
)

# Set up logging
logging.basicConfig(level=logging.INFO)

async def test_comprehensive_memory_system():
    """
    Comprehensive test for sliding window + time-weighted retrieval.
    """
    print("=" * 70)
    print("COMPREHENSIVE MEMORY SYSTEM TEST")
    print("=" * 70)
    print(f"MAX_MEMORIES_PER_CHAT: {MAX_MEMORIES_PER_CHAT}")
    print(f"ENABLE_ORDER_RANKING: {ENABLE_ORDER_RANKING}")
    print(f"ORDER_WEIGHT_FACTOR: {ORDER_WEIGHT_FACTOR}")
    print("-" * 70)

    # Initialize Qdrant collection
    init_qdrant_collection()

    user_id = "comprehensive_test_user"
    chat_id = f"comprehensive_test_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

    print(f"Using user_id: {user_id}, chat_id: {chat_id}")

    # Test 1: Sliding Window Functionality
    print(f"\n1. TESTING SLIDING WINDOW (storing {MAX_MEMORIES_PER_CHAT + 3} messages)")

    test_messages = []
    for i in range(MAX_MEMORIES_PER_CHAT + 3):  # Store more than the limit
        question = f"Question {i+1}: What is the price of stock symbol {chr(65+i%26)}APL?"
        answer = f"Answer {i+1}: Stock {chr(65+i%26)}APL is trading at ${150 + i}.00"
        test_messages.append((question, answer))

    # Store messages with small delays
    for i, (question, answer) in enumerate(test_messages):
        messages = [
            {"role": "user", "content": question},
            {"role": "assistant", "content": answer}
        ]

        success = await store_user_message(
            user_id=user_id,
            chat_id=chat_id,
            messages=messages,
            run_async=False
        )

        if success:
            print(f"  ✓ Stored message {i+1}")
        else:
            print(f"  ✗ Failed to store message {i+1}")

        # Small delay to create different timestamps
        await asyncio.sleep(0.1)

    # Wait for operations to complete
    await asyncio.sleep(2)

    # Check memory count
    client = get_memory_store()
    if client:
        memory_count = await get_chat_memory_count(client, user_id, chat_id)
        print(f"\n  Memory count after storing {len(test_messages)} messages: {memory_count}")

        if memory_count <= MAX_MEMORIES_PER_CHAT:
            print(f"  ✓ Sliding window working: {memory_count} <= {MAX_MEMORIES_PER_CHAT}")
        else:
            print(f"  ✗ Sliding window failed: {memory_count} > {MAX_MEMORIES_PER_CHAT}")

    # Test 2: Time-Weighted Retrieval
    print(f"\n2. TESTING TIME-WEIGHTED RETRIEVAL")

    search_results = await search_memories(
        user_id=user_id,
        query="stock price",
        limit=10,
        include_conversation_history=True,
        chat_id=chat_id
    )

    results = search_results.get("results", [])
    print(f"  Search returned {len(results)} results")

    if results:
        print(f"\n  Results with time-weighted scores:")
        for i, result in enumerate(results[:5]):  # Show top 5
            memory_content = result.get("memory", "")
            combined_score = result.get("score", 0)
            original_score = result.get("original_score", 0)
            time_weight = result.get("time_weight", 1.0)
            timestamp = result["metadata"].get("timestamp", "")

            print(f"    {i+1}. Combined: {combined_score:.3f} | Semantic: {original_score:.3f} | Time: {time_weight:.3f}")
            print(f"       Content: {memory_content[:50]}...")
            print(f"       Timestamp: {timestamp}")

        # Verify order ranking is working
        if ENABLE_ORDER_RANKING and len(results) > 1:
            score_differences = [abs(r.get("score", 0) - r.get("original_score", 0)) for r in results]
            if any(diff > 0.001 for diff in score_differences):
                print(f"\n  ✓ Order ranking is affecting scores")
            else:
                print(f"\n  ⚠ Order ranking doesn't seem to be affecting scores significantly")

        # Check that results are properly ordered by combined score
        scores = [r.get("score", 0) for r in results]
        is_ordered = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
        if is_ordered:
            print(f"  ✓ Results are properly ordered by combined score")
        else:
            print(f"  ⚠ Results are not properly ordered by combined score")

    # Test 3: Verify Chat Isolation
    print(f"\n3. TESTING CHAT ISOLATION")

    # Create a different chat and store some messages
    chat_id_2 = f"isolation_test_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

    isolation_messages = [
        ("Different chat question 1", "Different chat answer 1"),
        ("Different chat question 2", "Different chat answer 2")
    ]

    for question, answer in isolation_messages:
        messages = [
            {"role": "user", "content": question},
            {"role": "assistant", "content": answer}
        ]

        await store_user_message(
            user_id=user_id,
            chat_id=chat_id_2,
            messages=messages,
            run_async=False
        )

    # Search in original chat - should not return messages from chat_id_2
    original_chat_results = await search_memories(
        user_id=user_id,
        query="different chat",
        limit=10,
        include_conversation_history=True,
        chat_id=chat_id
    )

    # Search in new chat - should return messages from chat_id_2
    new_chat_results = await search_memories(
        user_id=user_id,
        query="different chat",
        limit=10,
        include_conversation_history=True,
        chat_id=chat_id_2
    )

    original_results = original_chat_results.get("results", [])
    new_results = new_chat_results.get("results", [])

    print(f"  Original chat search results: {len(original_results)}")
    print(f"  New chat search results: {len(new_results)}")

    # Check that "different chat" messages don't appear in original chat
    isolation_working = True
    for result in original_results:
        if "different chat" in result.get("memory", "").lower():
            isolation_working = False
            break

    if isolation_working and len(new_results) > 0:
        print(f"  ✓ Chat isolation working correctly")
    else:
        print(f"  ⚠ Chat isolation may not be working correctly")

    # Test 4: Backward Compatibility
    print(f"\n4. TESTING BACKWARD COMPATIBILITY")

    # Test search without time weighting by temporarily disabling it
    print(f"  Testing search functionality maintains existing format...")

    # All results should have the expected fields
    if results:
        required_fields = ["memory_id", "memory", "metadata", "messages", "score"]
        all_have_fields = all(
            all(field in result for field in required_fields)
            for result in results
        )

        if all_have_fields:
            print(f"  ✓ All results have required fields")
        else:
            print(f"  ✗ Some results missing required fields")

        # Check that new fields are present when order ranking is enabled
        if ENABLE_ORDER_RANKING:
            order_fields = ["original_score", "order_rank", "order_weight"]
            all_have_order_fields = all(
                all(field in result for field in order_fields)
                for result in results
            )

            if all_have_order_fields:
                print(f"  ✓ All results have order ranking fields")
            else:
                print(f"  ✗ Some results missing order ranking fields")

    print(f"\n" + "=" * 70)
    print("COMPREHENSIVE TEST COMPLETED")
    print("=" * 70)

    # Summary
    print(f"\nSUMMARY:")
    print(f"✓ Sliding window: Maintains max {MAX_MEMORIES_PER_CHAT} memories per chat")
    print(f"✓ Order ranking: {'Enabled' if ENABLE_ORDER_RANKING else 'Disabled'}")
    print(f"✓ Chat isolation: Separate memory spaces per chat_id")
    print(f"✓ Backward compatibility: Existing functionality preserved")
    print(f"✓ Combined scoring: Semantic similarity + order ranking")

if __name__ == "__main__":
    asyncio.run(test_comprehensive_memory_system())
