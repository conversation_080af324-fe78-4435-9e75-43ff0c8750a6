"""
Test script for the sliding window memory management functionality.
"""
import asyncio
import datetime
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.memory.memory_manager import (
    store_user_message, search_memories, get_chat_memory_count, get_memory_store,
    calculate_order_rank_weight, calculate_combined_score_with_order
)
from app.memory.init_qdrant import init_qdrant_collection
from app.memory.langmem_config import (
    MAX_MEMORIES_PER_CHAT, ENABLE_ORDER_RANKING, ORDER_WEIGHT_FACTOR
)
from app.logging_custom_directory.logger_custom import logger

# Set up logging
logging.basicConfig(level=logging.INFO)

async def test_sliding_window():
    """
    Test the sliding window functionality by storing more than MAX_MEMORIES_PER_CHAT messages
    and verifying that only the most recent ones are retained.
    """
    print(f"Testing sliding window with MAX_MEMORIES_PER_CHAT = {MAX_MEMORIES_PER_CHAT}")

    # Initialize Qdrant collection
    init_qdrant_collection()

    # Create test user and chat IDs
    user_id = "test_sliding_window_user"
    chat_id = f"test_chat_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

    print(f"Using user_id: {user_id}, chat_id: {chat_id}")

    # Get Qdrant client
    client = get_memory_store()
    if not client:
        print("Warning: Qdrant client not available, testing with fallback store only")

    # Store messages exceeding the sliding window limit
    num_messages_to_store = MAX_MEMORIES_PER_CHAT + 5  # Store 5 more than the limit
    print(f"Storing {num_messages_to_store} messages...")

    stored_messages = []
    for i in range(num_messages_to_store):
        message_content = f"Test message {i+1}: What is the current price of AAPL stock?"
        response_content = f"Response {i+1}: AAPL is currently trading at $150.{i:02d}"

        messages = [
            {"role": "user", "content": message_content},
            {"role": "assistant", "content": response_content}
        ]

        stored_messages.append((message_content, response_content))

        # Store the message
        success = await store_user_message(
            user_id=user_id,
            chat_id=chat_id,
            messages=messages,
            run_async=False  # Wait for completion to ensure proper testing
        )

        if success:
            print(f"✓ Stored message {i+1}")
        else:
            print(f"✗ Failed to store message {i+1}")

        # Small delay to ensure different timestamps
        await asyncio.sleep(0.1)

    # Wait a bit for all operations to complete
    await asyncio.sleep(2)

    # Check memory count if using Qdrant
    if client:
        try:
            memory_count = await get_chat_memory_count(client, user_id, chat_id)
            print(f"\nMemory count in Qdrant: {memory_count}")

            if memory_count <= MAX_MEMORIES_PER_CHAT:
                print(f"✓ Sliding window working: {memory_count} <= {MAX_MEMORIES_PER_CHAT}")
            else:
                print(f"✗ Sliding window failed: {memory_count} > {MAX_MEMORIES_PER_CHAT}")
        except Exception as e:
            print(f"Error checking memory count: {e}")

    # Test search functionality
    print(f"\nTesting search functionality...")
    try:
        search_results = await search_memories(
            user_id=user_id,
            query="AAPL stock price",
            limit=20,  # Request more than the sliding window limit
            include_conversation_history=True,
            chat_id=chat_id
        )

        results = search_results.get("results", [])
        print(f"Search returned {len(results)} results")

        if len(results) <= MAX_MEMORIES_PER_CHAT:
            print(f"✓ Search respects sliding window: {len(results)} <= {MAX_MEMORIES_PER_CHAT}")
        else:
            print(f"✗ Search doesn't respect sliding window: {len(results)} > {MAX_MEMORIES_PER_CHAT}")

        # Check if results are ordered by recency (most recent first)
        if len(results) > 1:
            timestamps = [r["metadata"].get("timestamp", "") for r in results]
            is_ordered = all(timestamps[i] >= timestamps[i+1] for i in range(len(timestamps)-1))

            if is_ordered:
                print("✓ Results are ordered by recency (most recent first)")
            else:
                print("✗ Results are not properly ordered by recency")

        # Display the most recent few results
        print(f"\nMost recent {min(3, len(results))} memories:")
        for i, result in enumerate(results[:3]):
            memory_content = result.get("memory", "")
            timestamp = result["metadata"].get("timestamp", "")
            print(f"  {i+1}. {memory_content[:50]}... (timestamp: {timestamp})")

        # Verify that the oldest messages are no longer present
        if len(stored_messages) > MAX_MEMORIES_PER_CHAT:
            oldest_messages = stored_messages[:len(stored_messages) - MAX_MEMORIES_PER_CHAT]
            newest_messages = stored_messages[-MAX_MEMORIES_PER_CHAT:]

            # Check if any of the oldest messages are still in results
            oldest_found = False
            for old_msg, _ in oldest_messages:
                for result in results:
                    if old_msg in result.get("memory", ""):
                        oldest_found = True
                        break
                if oldest_found:
                    break

            if not oldest_found:
                print("✓ Oldest messages successfully removed from storage")
            else:
                print("✗ Some oldest messages still found in storage")

            # Check if the newest messages are present
            newest_found_count = 0
            for new_msg, _ in newest_messages:
                for result in results:
                    if new_msg in result.get("memory", ""):
                        newest_found_count += 1
                        break

            print(f"✓ Found {newest_found_count}/{len(newest_messages)} of the newest messages")

    except Exception as e:
        print(f"Error during search test: {e}")

    print(f"\nSliding window test completed!")

async def test_order_ranking_retrieval():
    """
    Test the order-based retrieval functionality.
    """
    print(f"\n" + "="*60)
    print(f"Testing Order-Based Retrieval")
    print(f"ENABLE_ORDER_RANKING: {ENABLE_ORDER_RANKING}")
    print(f"ORDER_WEIGHT_FACTOR: {ORDER_WEIGHT_FACTOR}")
    print(f"="*60)

    # Initialize Qdrant collection
    init_qdrant_collection()

    # Create test user and chat IDs
    user_id = "test_time_weighted_user"
    chat_id = f"test_time_weighted_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

    print(f"Using user_id: {user_id}, chat_id: {chat_id}")

    # Store messages with different time intervals to test time weighting
    test_messages = [
        ("What is the current price of AAPL?", "AAPL is trading at $150.00"),
        ("Tell me about Tesla stock performance", "Tesla has shown strong growth this quarter"),
        ("What are the best dividend stocks?", "Consider MSFT, JNJ, and KO for dividends"),
        ("How is the crypto market doing?", "Bitcoin is up 5% today, showing bullish momentum"),
        ("What's the latest on NVDA?", "NVIDIA reported strong earnings, stock up 8%")
    ]

    print(f"\nStoring {len(test_messages)} messages with time delays...")

    # Store messages with delays to create different timestamps
    for i, (question, answer) in enumerate(test_messages):
        messages = [
            {"role": "user", "content": question},
            {"role": "assistant", "content": answer}
        ]

        success = await store_user_message(
            user_id=user_id,
            chat_id=chat_id,
            messages=messages,
            run_async=False
        )

        if success:
            print(f"✓ Stored message {i+1}: {question[:30]}...")
        else:
            print(f"✗ Failed to store message {i+1}")

        # Add delay between messages to create time differences
        await asyncio.sleep(1)

    # Wait a bit for all operations to complete
    await asyncio.sleep(2)

    print(f"\nTesting time-weighted search...")

    # Test search with time weighting enabled
    search_results = await search_memories(
        user_id=user_id,
        query="stock price performance",
        limit=10,
        include_conversation_history=True,
        chat_id=chat_id
    )

    results = search_results.get("results", [])
    print(f"\nSearch returned {len(results)} results")

    if results:
        print(f"\nResults ordered by combined score (semantic + time weighting):")
        for i, result in enumerate(results):
            memory_content = result.get("memory", "")
            combined_score = result.get("score", 0)
            original_score = result.get("original_score", 0)
            time_weight = result.get("time_weight", 1.0)
            timestamp = result["metadata"].get("timestamp", "")

            print(f"  {i+1}. Score: {combined_score:.4f} (semantic: {original_score:.4f}, time: {time_weight:.4f})")
            print(f"     Content: {memory_content[:50]}...")
            print(f"     Timestamp: {timestamp}")
            print()

        # Verify that more recent memories have higher time weights
        if len(results) > 1:
            time_weights = [r.get("time_weight", 1.0) for r in results]
            timestamps = [r["metadata"].get("timestamp", "") for r in results]

            # Check if time weights are generally decreasing with older timestamps
            recent_avg = sum(time_weights[:2]) / 2 if len(time_weights) >= 2 else time_weights[0]
            older_avg = sum(time_weights[-2:]) / 2 if len(time_weights) >= 2 else time_weights[-1]

            if recent_avg >= older_avg:
                print("✓ Time weighting working: Recent memories have higher time weights")
            else:
                print("⚠ Time weighting may not be working as expected")

        # Test that combined scores are different from semantic scores when order ranking is enabled
        if ENABLE_ORDER_RANKING:
            score_differences = [abs(r.get("score", 0) - r.get("original_score", 0)) for r in results]
            if any(diff > 0.001 for diff in score_differences):
                print("✓ Order ranking is affecting scores")
            else:
                print("⚠ Order ranking doesn't seem to be affecting scores significantly")

    # Test order rank weight calculation directly
    print(f"\nTesting order rank weight calculation:")
    total_memories = 5

    # Test different order ranks
    test_ranks = [
        ("Most recent (rank 0)", 0),
        ("Second most recent (rank 1)", 1),
        ("Middle (rank 2)", 2),
        ("Second oldest (rank 3)", 3),
        ("Oldest (rank 4)", 4)
    ]

    for label, order_rank in test_ranks:
        order_weight = calculate_order_rank_weight(order_rank, total_memories)
        print(f"  {label}: order_weight = {order_weight:.4f}")

    # Test combined score calculation
    print(f"\nTesting combined score calculation:")
    semantic_score = 0.8
    for label, order_rank in test_ranks:
        combined_score = calculate_combined_score_with_order(semantic_score, order_rank, total_memories)
        order_weight = calculate_order_rank_weight(order_rank, total_memories)
        print(f"  {label}: semantic={semantic_score:.4f}, order={order_weight:.4f}, combined={combined_score:.4f}")

    print(f"\nOrder-based retrieval test completed!")

async def run_all_tests():
    """Run both sliding window and order-based retrieval tests."""
    await test_sliding_window()
    await test_order_ranking_retrieval()

if __name__ == "__main__":
    asyncio.run(run_all_tests())
