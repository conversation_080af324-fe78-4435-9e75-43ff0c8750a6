
agentops==0.4.6
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.10.5
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
arxiv==2.1.3
asgiref==3.8.1
asttokens==3.0.0
asyncpg==0.29.0
attrs==25.3.0
Authlib==1.5.2
autogen-agentchat==0.5.1
autogen-core==0.5.1
autogen-ext==0.5.1
azure-ai-inference==1.0.0b9
azure-common==1.1.28
azure-core==1.33.0
azure-identity==1.21.0
azure-search-documents==11.5.2
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.3
blockbuster==1.5.24
Brotli==1.1.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.8
cloudpickle==3.1.1
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.3.1
cryptography==44.0.2
cssselect2==0.8.0
cssutils==2.11.1
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.14
decorator==5.2.1
Deprecated==1.2.18
dirtyjson==1.0.8
diskcache==5.6.3
distro==1.9.0
docker==7.1.0
docopt==0.6.2
durationpy==0.9
emoji==2.14.1
eval_type_backport==0.2.2
executing==2.2.0
faiss-cpu==1.8.0.post1
fastapi==0.115.12
fastapi-cli==0.0.4
feedparser==6.0.11
filelock==3.18.0
filetype==1.2.0
firecrawl-py==1.16.0
FLAML==2.3.4
flatbuffers==25.2.10
fonttools==4.57.0
forbiddenfruit==0.1.4
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2025.3.2
g==0.0.7
google-api-core==2.24.2
google-auth==2.31.0
google-cloud==0.34.0
google-cloud-core==2.4.3
google-cloud-storage==3.1.0
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.63.2
greenlet==3.1.1
grpcio==1.71.0
grpcio-health-checking==1.71.0
grpcio-tools==1.71.0
h11==0.14.0
html5lib==1.1
htmldocx==0.0.6
httpcore==1.0.7
httptools==0.6.4
httpx==0.27.0
httpx-sse==0.4.0
huggingface-hub==0.23.4
humanfriendly==10.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
ipykernel==6.29.5
ipython==9.1.0
ipython_pygments_lexers==1.1.1
isodate==0.7.2
jedi==0.19.2
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
json5==0.12.0
json_repair==0.40.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema_rs==0.29.1
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.8
kubernetes==32.0.1
langchain==0.3.22
langchain-community==0.3.20
langchain-core==0.3.50
langchain-huggingface==0.1.0
langchain-openai==0.2.0
langchain-text-splitters==0.3.7
langchain-weaviate==0.0.3
langchainhub==0.1.21
langdetect==1.0.9
langgraph==0.3.25
# langgraph-api==0.1.21
langgraph-checkpoint==2.0.25
langgraph-cli==0.2.7
langgraph-prebuilt==0.1.8
# langgraph-runtime-inmem==0.0.7
langgraph-sdk==0.1.63
langsmith==0.1.147
lark==1.2.2
llama-index==0.10.44
llama-index-agent-openai==0.2.7
llama-index-cli==0.1.12
llama-index-core==0.10.44
llama-index-embeddings-huggingface==0.2.2
llama-index-embeddings-openai==0.1.10
llama-index-indices-managed-llama-cloud==0.1.6
llama-index-legacy==0.9.48
llama-index-llms-openai==0.1.22
llama-index-multi-modal-llms-openai==0.1.6
llama-index-program-openai==0.1.6
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.25
llama-index-readers-llama-parse==0.1.4
llama-index-vector-stores-chroma==0.1.9
llama-index-vector-stores-postgres==0.1.10
llama-parse==0.4.4
llamaindex-py-client==0.1.19
loguru==0.7.3
lxml==5.3.2
lxml_html_clean==0.4.1
Markdown==3.7
markdown-it-py==3.0.0
markdown2==2.5.3
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.8.2
matplotlib-inline==0.1.6
md2pdf==1.0.1
mdurl==0.1.2
minijinja==2.9.0
mistune==3.1.3
mmh3==5.1.0
monotonic==1.6
more-itertools==10.6.0
mpmath==1.3.0
msal==1.32.0
msal-extensions==1.3.1
multidict==6.3.2
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.8.1
numpy==1.26.4
oauthlib==3.2.2
olefile==0.47
onnxruntime==1.21.0
openai==1.70.0
langmem
qdrant-client
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-grpc==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-instrumentation==0.52b1
opentelemetry-instrumentation-asgi==0.52b1
opentelemetry-instrumentation-fastapi==0.52b1
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
opentelemetry-util-http==0.52b1
ordered-set==4.1.0
orjson==3.10.16
ormsgpack==1.9.1
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdfkit==1.0.0
pgvector==0.2.5
pillow==11.1.0
platformdirs==4.3.7
posthog==3.23.0
prompt_toolkit==3.0.51
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psutil==6.0.0
psycopg2-binary==2.9.9
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pyautogen==0.2.27
pycparser==2.22
pydantic==2.11.2
pydantic-settings==2.8.1
pydantic_core==2.33.1
pydyf==0.11.0
Pygments==2.19.1
PyJWT==2.9.0
PyMuPDF==1.24.5
PyMuPDFb==1.24.3
pyparsing==3.1.1
pypdf==4.2.0
pyphen==0.17.2
PyPika==0.48.9
pyproject_hooks==1.2.0
pyrate-limiter==3.7.0
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.0
python-http-client==3.3.7
python-iso639==2025.2.18
python-magic==0.4.27
python-multipart==0.0.9
python-oxmsg==0.0.2
pytz==2023.3.post1
#pywin32==310
PyYAML==6.0.1
pyzmq==25.1.2
RapidFuzz==3.13.0
redis==5.0.1
regex==2023.10.3
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3986==2.0.0
rich==13.7.1
rsa==4.9
safetensors==0.4.1
scikit-learn==1.3.2
scipy==1.11.4
sec-downloader==0.11.1
sec-edgar-downloader==5.0.3
sec-parser==0.58.1
sendgrid==6.10.0
sentence-transformers==2.7.0
setuptools==78.1.0
sgmllib3k==1.0.0
shellingham==1.5.4
simsimd==4.4.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.23
sse-starlette==2.1.3
stack-data==0.6.3
starkbank-ecdsa==2.2.0
starlette==0.46.2
striprtf==0.0.26
structlog==25.3.0
style==1.1.0
sympy==1.13.1
tabulate==0.9.0
tavily-python==0.6.0
tenacity==8.5.0
termcolor==2.4.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tinycss2==1.4.0
tinyhtml5==2.0.0
tokenizers==0.19.1
torch==2.6.0
tornado==6.4.2
tqdm==4.66.1
traitlets==5.14.3
transformers==4.41.2
typer==0.15.2
types-requests==2.32.0.********
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.1
tzdata==2025.2
unstructured==0.17.2
unstructured-client==0.32.1
update==0.0.1
urllib3==2.0.7
uvicorn==0.34.2
validators==0.33.0
watchfiles==1.0.4
wcwidth==0.2.13
weasyprint==65.0
weaviate-client==4.7.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==12.0
win32_setctime==1.2.0
wkhtmltopdf==0.2
word2number==1.1
wrapt==1.17.2
xgboost==2.0.2
xxhash==3.5.0
yarl==1.9.3
zipp==3.21.0
zopfli==0.2.3.post1
zstandard==0.23.0
mem0ai==0.1.94