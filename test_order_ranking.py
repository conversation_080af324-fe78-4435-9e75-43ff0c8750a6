"""
Simple test script for order-based retrieval functionality.
"""
import asyncio
import datetime
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.memory.memory_manager import (
    store_user_message, search_memories, calculate_order_rank_weight, calculate_combined_score_with_order
)
from app.memory.init_qdrant import init_qdrant_collection
from app.memory.langmem_config import (
    ENABLE_ORDER_RANKING, ORDER_WEIGHT_FACTOR
)

# Set up logging
logging.basicConfig(level=logging.INFO)

async def test_order_ranking_simple():
    """
    Simple test for order-based retrieval functionality.
    """
    print(f"Testing Order-Based Retrieval")
    print(f"ENABLE_ORDER_RANKING: {ENABLE_ORDER_RANKING}")
    print(f"ORDER_WEIGHT_FACTOR: {ORDER_WEIGHT_FACTOR}")
    print("-" * 50)
    
    # Test order rank weight calculation directly
    print(f"\n1. Testing order rank weight calculation:")
    total_memories = 5
    
    # Test different order ranks
    test_ranks = [
        ("Most recent (rank 0)", 0),
        ("Second most recent (rank 1)", 1),
        ("Middle (rank 2)", 2),
        ("Second oldest (rank 3)", 3),
        ("Oldest (rank 4)", 4)
    ]
    
    for label, order_rank in test_ranks:
        order_weight = calculate_order_rank_weight(order_rank, total_memories)
        print(f"  {label:25}: order_weight = {order_weight:.4f}")
    
    # Test combined score calculation
    print(f"\n2. Testing combined score calculation:")
    semantic_score = 0.8
    for label, order_rank in test_ranks:
        combined_score = calculate_combined_score_with_order(semantic_score, order_rank, total_memories)
        order_weight = calculate_order_rank_weight(order_rank, total_memories)
        print(f"  {label:25}: semantic={semantic_score:.3f}, order={order_weight:.3f}, combined={combined_score:.3f}")

    # Test with actual memory storage and retrieval
    print(f"\n3. Testing with actual memory storage:")

    # Initialize Qdrant collection
    init_qdrant_collection()

    user_id = "test_order_user"
    chat_id = f"test_order_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

    # Store a few test messages with different timestamps
    test_messages = [
        "What is the current price of AAPL stock?",
        "Tell me about Tesla's recent performance",
        "What are the best dividend stocks to buy?",
        "How do I analyze a company's financial statements?",
        "What is the difference between growth and value investing?"
    ]

    print(f"Storing {len(test_messages)} test messages...")
    for i, question in enumerate(test_messages):
        messages = [
            {"role": "user", "content": question},
            {"role": "assistant", "content": f"Response to question {i+1}: {question}"}
        ]

        success = await store_user_message(
            user_id=user_id,
            chat_id=chat_id,
            messages=messages,
            run_async=False
        )

        if success:
            print(f"  ✓ Stored: {question[:40]}...")
        else:
            print(f"  ✗ Failed: {question[:40]}...")

        # Small delay to create different timestamps
        await asyncio.sleep(0.5)

    # Search and check results
    print(f"\n4. Testing search with order ranking:")
    search_results = await search_memories(
        user_id=user_id,
        query="stock investing",
        limit=5,
        include_conversation_history=True,
        chat_id=chat_id
    )

    results = search_results.get("results", [])
    print(f"Found {len(results)} results:")

    for i, result in enumerate(results):
        memory_content = result.get("memory", "")
        combined_score = result.get("score", 0)
        original_score = result.get("original_score", 0)
        order_rank = result.get("order_rank", -1)
        order_weight = result.get("order_weight", 1.0)

        print(f"  {i+1}. Score: {combined_score:.3f} (semantic: {original_score:.3f}, order_rank: {order_rank}, order_weight: {order_weight:.3f})")
        print(f"     Content: {memory_content[:50]}...")

    # Verify order ranking is working
    if len(results) > 1 and ENABLE_ORDER_RANKING:
        score_differences = [abs(r.get("score", 0) - r.get("original_score", 0)) for r in results]
        if any(diff > 0.001 for diff in score_differences):
            print(f"\n✓ Order ranking is affecting scores (differences found)")
        else:
            print(f"\n⚠ Order ranking doesn't seem to be affecting scores significantly")
        
        # Check if order ranks are properly assigned
        order_ranks = [r.get("order_rank", -1) for r in results if r.get("order_rank", -1) >= 0]
        if order_ranks:
            print(f"✓ Order ranks assigned: {order_ranks}")
        else:
            print(f"⚠ No order ranks found in results")

    print(f"\nOrder-based retrieval test completed!")

if __name__ == "__main__":
    asyncio.run(test_order_ranking_simple())
