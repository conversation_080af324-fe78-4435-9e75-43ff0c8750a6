pipeline {
    agent any

    stages {
        stage('Remote SSH Execution') {
            steps {
                // Use withCredentials to access your username and password secure
                withCredentials([usernamePassword(credentialsId: 'cfbb74a1-756e-48b4-8553-4466fd0633df', usernameVariable: 'SSH_USER', passwordVariable: 'SSH_PASS')]) {
                    script {
                        // Define the remote settings
                        def remote = [:]
                        remote.name = 'scripts-user-158-server-cred'
                        remote.host = '**************'
                        remote.user = SSH_USER
                        remote.password = SSH_PASS
                        remote.allowAnyHosts = true // Set to false for production

                        // Execute the script on the remote host directly
                        sshCommand remote: remote, command: 'cd /var/analytics-supabase/deploy-scripts && ./autogen-v2-dev.traderverse.io-deploy.sh'
                    }
                }
            }
        }
    }
}
