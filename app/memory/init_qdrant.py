"""
Script to initialize Qdrant collection for memory storage.
"""
import os
import sys
import logging
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import UnexpectedResponse

from app.memory.langmem_config import QDRANT_URL, QDRANT_COLLECTION_NAME

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_qdrant_collection():
    """
    Initialize Qdrant collection for memory storage.
    """
    try:
        # Connect to Qdrant
        client = QdrantClient(url=QDRANT_URL)
        logger.info(f"Connected to Qdrant at {QDRANT_URL}")
        
        # Check if collection exists
        try:
            collection_info = client.get_collection(collection_name=QDRANT_COLLECTION_NAME)
            logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' already exists")
            return True
        except UnexpectedResponse:
            # Collection doesn't exist, create it
            logger.info(f"Creating collection '{QDRANT_COLLECTION_NAME}'")
            
            # Create collection with appropriate vector configuration
            client.create_collection(
                collection_name=QDRANT_COLLECTION_NAME,
                vectors_config=models.VectorParams(
                    size=1536,  # OpenAI embedding dimension
                    distance=models.Distance.COSINE
                )
            )
            
            # Create indexes for efficient filtering
            client.create_payload_index(
                collection_name=QDRANT_COLLECTION_NAME,
                field_name="metadata.user_id",
                field_schema=models.PayloadSchemaType.KEYWORD
            )
            
            client.create_payload_index(
                collection_name=QDRANT_COLLECTION_NAME,
                field_name="metadata.chat_id",
                field_schema=models.PayloadSchemaType.KEYWORD
            )
            
            client.create_payload_index(
                collection_name=QDRANT_COLLECTION_NAME,
                field_name="metadata.memory_type",
                field_schema=models.PayloadSchemaType.KEYWORD
            )
            
            client.create_payload_index(
                collection_name=QDRANT_COLLECTION_NAME,
                field_name="metadata.timestamp",
                field_schema=models.PayloadSchemaType.DATETIME
            )
            
            logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' created successfully")
            return True
            
    except Exception as e:
        logger.error(f"Error initializing Qdrant collection: {e}")
        return False

if __name__ == "__main__":
    init_qdrant_collection()
