import os
from mem0 import Memory
from app.logging_custom_directory.logger_custom import logger
from dotenv import load_dotenv

# --- Configuration constants ---
QDRANT_URL      = os.getenv("QDRANT_URL", "http://**************:6333")
COLLECTION_NAME = os.getenv("QDRANT_COLLECTION", "mem0_memories")
EMBED_DIM       = 1536  # text-embedding-3-small → 1536-dim

def setup_mem0() -> Memory:
    """
    Set up and configure a mem0 Memory instance.

    Returns:
        Memory: Configured mem0 Memory instance
    """
    load_dotenv()  # loads OPENAI_API_KEY, optionally QDRANT_URL/COLLECTION
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        raise EnvironmentError("OPENAI_API_KEY environment variable not set")

    # Mem0 configuration
    config = {
    "vector_store": {
        "provider": "qdrant",
        "config": {
             "url": QDRANT_URL,
             "collection_name": COLLECTION_NAME,
             "embedding_model_dims": EMBED_DIM
            # "host": os.getenv("QDRANT_HOST", "**************"),
            # "port": int(os.getenv("QDRANT_PORT", "6333")),
            # "collection_name": os.getenv("MEM0_COLLECTION_NAME", "mem0_memories"),
        }
    },
    "embedder": {
        "provider": "openai",
        "config": {
            "model": os.getenv("MEM0_EMBEDDING_MODEL", "text-embedding-3-small"),
            "api_key": os.getenv("OPENAI_API_KEY"),
        }
    },
    "llm": {
        "provider": "openai",
        "config": {
            "model": os.getenv("MEM0_LLM_MODEL", "gpt-4o-mini"),
            "api_key": os.getenv("OPENAI_API_KEY"),
        }
    }
}

    # Initialize and return Mem0 instance
    return Memory.from_config(config)

def create_mem0_instance():
    """
    Create and return a configured mem0 Memory instance.

    Returns:
        Memory: Configured mem0 Memory instance
    """
    return setup_mem0()

if __name__ == "__main__":
    # For testing purposes
    mem = create_mem0_instance()
    print(f"Mem0 instance created successfully: {mem}")



# Memory search and storage settings
DEFAULT_SEARCH_LIMIT = 5
MAX_SEARCH_LIMIT = 10
MEMORY_RELEVANCE_THRESHOLD = 0.7
