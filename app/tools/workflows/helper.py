import aiohttp
import json
import openai
from datetime import datetime, timedelta
import asyncio
from functools import lru_cache
import time

from app.core.config import settings
from langchain_core.tools import tool

# Create a global session for reuse across API calls
_SESSION = None

async def get_session():
    """Get or create a shared aiohttp ClientSession."""
    global _SESSION
    if _SESSION is None or _SESSION.closed:
        _SESSION = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300),
            timeout=aiohttp.ClientTimeout(total=30)
        )
    return _SESSION

# # Cache for API responses to avoid redundant calls
# _API_CACHE = {}
# _CACHE_TTL = 300  # 5 minutes cache TTL

# @lru_cache(maxsize=256)
# def get_cache_key(endpoint, params_str):
#     """
#     Generate a cache key from endpoint and params.

#     Args:
#         endpoint: The API endpoint
#         params_str: JSON string representation of params (must be pre-converted)

#     Returns:
#         A string cache key
#     """
#     return f"{endpoint}:{params_str}"

# def prepare_cache_key(endpoint, params):
#     """Prepare parameters for the cached get_cache_key function."""
#     # Convert params dict to a sorted, stable string representation
#     param_str = json.dumps(params, sort_keys=True) if params else ""
#     # Call the cached function with the string representation
#     return get_cache_key(endpoint, param_str)

async def fetch_data(endpoint, params=None, year=None, quarter=None):
    """
    Fetch data from API

    Args:
        endpoint: API endpoint to call
        params: Dictionary of query parameters
        year: Optional year parameter for endpoints that require it (e.g., earnings transcripts)
        quarter: Optional quarter parameter for endpoints that require it (e.g., earnings transcripts)
        cache_ttl: Cache time-to-live in seconds (default: 5 minutes)

    Returns:
        JSON response from the API or error dictionary
    """
    if params is None:
        params = {}

    # Create a copy of params to avoid modifying the original
    params_copy = params.copy()

    # Add year and quarter to params if provided
    if year is not None:
        params_copy["year"] = year
    if quarter is not None:
        params_copy["quarter"] = quarter

    # Add API key to params
    params_copy["apikey"] = settings.FMP_API_KEY

    # # Check cache first
    # cache_key = prepare_cache_key(endpoint, params_copy)
    # cache_entry = _API_CACHE.get(cache_key)

    # # Return cached response if valid
    # if cache_entry and (time.time() - cache_entry["timestamp"] < cache_ttl):
    #     return cache_entry["data"]

    # Make API request if not in cache or expired
    url = f"{settings.FMP_BASE_URL}/{endpoint}"
    session = await get_session()

    try:
        async with session.get(url, params=params_copy) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                return {"error": f"API Error {response.status}: {error_text}"}
    except Exception as e:
        return {"error": f"Request error: {str(e)}"}

 
@tool(return_direct=True, parse_docstring=True)
async def comprehensive_company_analysis(symbol: str, query: str) -> str:
    """
    Performs a comprehensive financial analysis of a company including company overview,
    price targets, financial metrics, and analyst ratings.

    Args:
        symbol: Stock ticker symbol (e.g., 'AAPL' for Apple)
        query: Specific analysis question or focus area

    Returns:
        A comprehensive analysis report of the company
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "overview": None,
        "price_targets": {},
        "financial_metrics": {},
        "analyst_ratings": {}
    }

    # Fetch all data in a single gather call with optimized functions
    async def fetch_all_data():
        tasks = [
            fetch_data("profile", {"symbol": symbol}),
            fetch_data("price-target-summary", {"symbol": symbol}),
            fetch_data("price-target-consensus", {"symbol": symbol}),
            fetch_data("key-metrics", {"symbol": symbol, "period": "FY"}),
            fetch_data("ratios", {"symbol": symbol, "period": "FY"}),
            fetch_data("financial-scores", {"symbol": symbol}),
            fetch_data("grades", {"symbol": symbol, "limit": 10}),
            fetch_data("grades-historical", {"symbol": symbol, "limit": 10}),
            fetch_data("grades-consensus", {"symbol": symbol}),
            fetch_data("grades-news", {"symbol": symbol, "limit": 10})
        ]

        results = await asyncio.gather(*tasks)
        return results

    try:
        # Execute all API calls concurrently
        [
            company_profile,
            price_target_summary,
            price_target_consensus,
            key_metrics,
            financial_ratios,
            financial_scores,
            grades,
            historical_grades,
            grades_consensus,
            grade_news
        ] = await fetch_all_data()

        # Process company profile
        if isinstance(company_profile, list) and len(company_profile) > 0:
            company_data["overview"] = company_profile[0]

        # Process price targets
        if isinstance(price_target_summary, list) and len(price_target_summary) > 0:
            company_data["price_targets"]["summary"] = price_target_summary[0]
        if isinstance(price_target_consensus, list) and len(price_target_consensus) > 0:
            company_data["price_targets"]["consensus"] = price_target_consensus[0]

        # Process financial metrics
        if isinstance(key_metrics, list) and len(key_metrics) > 0:
            company_data["financial_metrics"]["key_metrics"] = key_metrics[0]
        if isinstance(financial_ratios, list) and len(financial_ratios) > 0:
            company_data["financial_metrics"]["ratios"] = financial_ratios[0]
        if isinstance(financial_scores, list) and len(financial_scores) > 0:
            company_data["financial_metrics"]["scores"] = financial_scores[0]

        # Process analyst ratings
        if isinstance(grades, list):
            company_data["analyst_ratings"]["recent_grades"] = grades[:10]
        if isinstance(historical_grades, list):
            company_data["analyst_ratings"]["historical_grades"] = historical_grades[:10]
        if isinstance(grades_consensus, list) and len(grades_consensus) > 0:
            company_data["analyst_ratings"]["consensus"] = grades_consensus[0]
        if isinstance(grade_news, list):
            company_data["analyst_ratings"]["news"] = grade_news[:10]

    except Exception as e:
        company_data["error"] = f"Error retrieving data: {str(e)}"

    return json.dumps(company_data, indent=2)
    # # 5. Composite Report Generation
    # try:
    #     # Configure OpenAI client with API key
    #     openai.api_key = settings.OPENAI_APIKEY

    #     # Define system message
    #     system_message = """
    #     You are a financial analysis expert. Your task is to synthesize financial data into a comprehensive,
    #     insightful report. Focus on the most meaningful metrics and trends.

    #     Structure your analysis as follows:
    #     1. Company Overview: Brief introduction to the company and its business.
    #     2. Market Position: Current stock performance, price targets, and market sentiment.
    #     3. Financial Health: Key metrics, ratios, and scores that show the company's financial condition.
    #     4. Analyst Sentiment: Summary of analyst ratings, upgrades/downgrades, and consensus.
    #     5. Investment Outlook: Synthesize the data into a forward-looking assessment.
    #     6. Key Risks and Opportunities: Highlight potential challenges and growth areas.

    #     Always maintain a balanced, objective tone and cite specific metrics from the data provided.
    #     """

    #     # Create user content with company data
    #     user_content = f"""
    #     Please analyze the following financial data for {symbol} and address this query: {query}

    #     Data:
    #     {json.dumps(company_data, indent=2)}

    #     Create a comprehensive analysis that provides valuable insights based on this data.
    #     """

    #     # Call OpenAI API
    #     response = openai.chat.completions.create(
    #         model="gpt-4.1-nano-2025-04-14",
    #         messages=[
    #             {"role": "system", "content": system_message},
    #             {"role": "user", "content": user_content}
    #         ]
    #     )

    #     # Extract and return the analysis report
    #     analysis_report = response.choices[0].message.content
    #     return analysis_report

    # except Exception as e:
    #     # For debugging purposes - return error details
    #     import traceback
    #     error_details = traceback.format_exc()
    #     return f"Error generating analysis: {str(e)}\n\nDetails:\n{error_details}"



# Define the market news analysis tool
# async def market_news_analysis(query: str = "Provide a comprehensive market news summary") -> str:
@tool(return_direct=True, parse_docstring=True)
async def market_news_analysis(query: str) -> str:
    """
    Analyzes recent market news across various categories including general news,
    stock market, crypto, forex, and press releases.

    Args:
        query: Specific analysis question or focus area

    Returns:
        A comprehensive analysis of market news
    """
    # Calculate date range for news (last 7 days)
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    from_date = week_ago.strftime("%Y-%m-%d")
    to_date = today.strftime("%Y-%m-%d")

    # Dictionary to store all collected news data
    news_data = {
        "query": query,
        "fmp_articles": [],
        "general_news": [],
        "press_releases": [],
        "stock_news": [],
        "crypto_news": [],
        "forex_news": []
    }

    # Optimized helper function to fetch multiple pages with caching
    async def fetch_multi_page_optimized(endpoint, base_params=None, pages=5, items_per_page=10):
        if base_params is None:
            base_params = {}

        # Add date range to params if not present
        if "from" not in base_params and endpoint.startswith("news/"):
            base_params["from"] = from_date
            base_params["to"] = to_date

        # Create tasks for all pages to fetch concurrently
        tasks = []
        for page in range(pages):
            params = base_params.copy()
            params["page"] = page
            params["limit"] = items_per_page
            tasks.append(fetch_data(endpoint, params))

        # Execute all page fetches concurrently
        results = await asyncio.gather(*tasks)

        # Process results
        all_items = []
        for data in results:
            if isinstance(data, list) and data:
                all_items.extend(data)
            elif isinstance(data, dict) and "error" in data:
                # If we have no results yet and got an error, return the error
                if not all_items:
                    return data
            elif not data:
                # No more data available for this page
                continue

        return all_items

    # Function to limit articles to avoid token limit issues
    def limit_articles(articles, max_count=100):
        """Limit articles to max_count, prioritizing newer ones"""
        if not isinstance(articles, list):
            return articles

        # Sort by published date if available (newest first)
        if articles and "publishedDate" in articles[0]:
            articles.sort(key=lambda x: x.get("publishedDate", ""), reverse=True)
        elif articles and "date" in articles[0]:
            articles.sort(key=lambda x: x.get("date", ""), reverse=True)

        return articles[:max_count]

    # Create tasks for fetching all news categories concurrently
    async def fetch_all_news():
        # Create tasks for all news categories
        tasks = {
            "fmp_articles": fetch_multi_page_optimized("fmp-articles", {}, pages=5, items_per_page=10),
            "general_news": fetch_multi_page_optimized("news/general-latest", {}, pages=5, items_per_page=10),
            "press_releases": fetch_multi_page_optimized("news/press-releases-latest", {}, pages=5, items_per_page=10),
            "stock_news": fetch_multi_page_optimized("news/stock-latest", {}, pages=5, items_per_page=10),
            "crypto_news": fetch_multi_page_optimized("news/crypto-latest", {}, pages=5, items_per_page=10),
            "forex_news": fetch_multi_page_optimized("news/forex-latest", {}, pages=5, items_per_page=10)
        }

        # Execute all tasks concurrently
        results = {}
        for category, task in tasks.items():
            try:
                results[category] = await task
            except Exception as e:
                results[category] = {"error": f"Error retrieving {category}: {str(e)}"}

        return results

    # Fetch all news data concurrently
    news_results = await fetch_all_news()

    # Process results
    for category, data in news_results.items():
        if isinstance(data, list):
            news_data[category] = limit_articles(data, 50)
        else:
            news_data[category] = data

    return json.dumps(news_data, indent=2)

    # # News Analysis using OpenAI
    # try:
    #     # Configure OpenAI client with API key
    #     openai.api_key = settings.OPENAI_APIKEY

    #     # Define system message
    #     system_message = """
    #     You are a financial news analyst and market expert. Your task is to synthesize recent financial news
    #     into a comprehensive, insightful report. Focus on identifying key trends, patterns, and significant events.

    #     Structure your analysis as follows:
    #     1. Market Overview: Brief summary of major market movements and trends.
    #     2. Breaking News: The most significant recent developments across markets.
    #     3. Sector Analysis: Insights grouped by market sectors showing notable activity.
    #     4. Asset Class Performance: Overview of stocks, cryptocurrencies, forex, and commodities.
    #     5. Key Themes: Identify recurring themes or narratives across different news sources.
    #     6. Forward Outlook: Potential implications and what to watch for in the coming days/weeks.

    #     Always maintain a balanced, objective tone and cite specific news items from the data provided.
    #     Prioritize recent news over older items.
    #     """

    #     # Create user content with news data
    #     user_content = f"""
    #     Please analyze the following market news and address this query: {query}

    #     News Data (from the past week):
    #     {json.dumps(news_data, indent=2)}

    #     Create a comprehensive analysis that provides valuable insights based on this news data.
    #     """

    #     # Call OpenAI API
    #     response = openai.chat.completions.create(
    #         model="gpt-4.1-nano-2025-04-14",
    #         messages=[
    #             {"role": "system", "content": system_message},
    #             {"role": "user", "content": user_content}
    #         ]
    #     )

    #     # Extract and return the analysis report
    #     analysis_report = response.choices[0].message.content
    #     return analysis_report

    # except Exception as e:
    #     # For debugging purposes - return error details
    #     import traceback
    #     error_details = traceback.format_exc()
    #     return f"Error generating market news analysis: {str(e)}\n\nDetails:\n{error_details}"

# async def market_movers_analysis(limit: int = 5, query: str = "Provide a comprehensive analysis of top market movers") -> str:
@tool(return_direct=True, parse_docstring=True)
async def market_movers_analysis(query: str, limit: int = 5) -> str:
    """
    Performs a comprehensive analysis of top market movers including biggest gainers,
    biggest losers, and most actively traded stocks, along with their news and press releases.

    Args:
        limit: Number of stocks to include in each category (default: 5)
        query: Specific analysis question or focus area

    Returns:
        A comprehensive analysis report of market movers
    """
    # Dictionary to store all collected data
    market_data = {
        "query": query,
        "biggest_gainers": [],
        "biggest_losers": [],
        "most_active": [],
        "news_and_releases": {}
    }

    # Fetch market movers data concurrently
    async def fetch_market_movers():
        # Create tasks for all market mover categories
        gainers_task = fetch_data("biggest-gainers")
        losers_task = fetch_data("biggest-losers")
        active_task = fetch_data("most-actives")

        # Execute all tasks concurrently
        gainers, losers, most_active = await asyncio.gather(
            gainers_task, losers_task, active_task
        )

        # Process results
        if isinstance(gainers, list):
            market_data["biggest_gainers"] = gainers[:limit]
        else:
            market_data["biggest_gainers"] = {"error": "Failed to retrieve biggest gainers"}

        if isinstance(losers, list):
            market_data["biggest_losers"] = losers[:limit]
        else:
            market_data["biggest_losers"] = {"error": "Failed to retrieve biggest losers"}

        if isinstance(most_active, list):
            market_data["most_active"] = most_active[:limit]
        else:
            market_data["most_active"] = {"error": "Failed to retrieve most active stocks"}

        # Get all unique symbols from the three categories
        all_symbols = set()
        for category in ["biggest_gainers", "biggest_losers", "most_active"]:
            if isinstance(market_data[category], list):
                for stock in market_data[category]:
                    if "symbol" in stock:
                        all_symbols.add(stock["symbol"])

        return all_symbols

    # Fetch news and press releases for a symbol
    async def fetch_symbol_news_and_releases(symbol):
        # Create tasks for news and press releases
        news_task = fetch_data("news/stock", {"symbols": symbol, "limit": 5})
        press_releases_task = fetch_data("news/press-releases", {"symbols": symbol, "limit": 3})

        # Execute tasks concurrently
        news, press_releases = await asyncio.gather(news_task, press_releases_task)

        # Process results
        result = {"news": [], "press_releases": []}

        if isinstance(news, list):
            result["news"] = news
        else:
            result["news"] = {"error": f"Error retrieving news for {symbol}"}

        if isinstance(press_releases, list):
            result["press_releases"] = press_releases
        else:
            result["press_releases"] = {"error": f"Error retrieving press releases for {symbol}"}

        return symbol, result

    # Main execution flow
    try:
        # Fetch market movers data
        all_symbols = await fetch_market_movers()

        # Fetch news and press releases for all symbols concurrently
        news_tasks = [fetch_symbol_news_and_releases(symbol) for symbol in all_symbols]
        news_results = await asyncio.gather(*news_tasks)

        # Process news results
        for symbol, data in news_results:
            market_data["news_and_releases"][symbol] = data

    except Exception as e:
        # Handle any unexpected errors
        market_data["error"] = f"Error processing market movers data: {str(e)}"

    return json.dumps(market_data, indent=2)
    # # 6. Generate Comprehensive Report
    # try:
    #     # Configure OpenAI client with API key
    #     openai.api_key = settings.OPENAI_APIKEY

    #     # Define system message
    #     system_message = """
    #     You are a financial market expert. Your task is to analyze market movers data and synthesize it into a
    #     comprehensive, insightful report focusing on stocks showing significant movement and their potential implications.

    #     Structure your analysis as follows:
    #     1. Market Overview: Brief summary of the current market conditions based on the movers.
    #     2. Top Gainers Analysis: Detailed analysis of the biggest gaining stocks, potential catalysts, and outlook.
    #     3. Top Losers Analysis: Examination of the biggest declining stocks, potential reasons, and future prospects.
    #     4. Most Active Stocks: Analysis of the most actively traded stocks and what's driving the high trading volume.
    #     5. News Impact: How recent news and press releases are affecting these market movers.
    #     6. Market Sentiment: Overall market sentiment and potential trading opportunities.
    #     7. Risk Assessment: Potential risks in the current market environment.

    #     For each stock, include relevant news and press releases in your analysis to provide context.
    #     Maintain a balanced, objective tone and cite specific metrics from the data provided.
    #     """

    #     # Create user content with market data
    #     user_content = f"""
    #     Please analyze the following market movers data and address this query: {query}

    #     Data:
    #     {json.dumps(market_data, indent=2)}

    #     Create a comprehensive analysis that provides valuable insights based on this data.
    #     """

    #     # Call OpenAI API
    #     response = openai.chat.completions.create(
    #         model="gpt-4.1-nano-2025-04-14",
    #         messages=[
    #             {"role": "system", "content": system_message},
    #             {"role": "user", "content": user_content}
    #         ]
    #     )

    #     # Extract and return the analysis report
    #     analysis_report = response.choices[0].message.content
    #     return analysis_report

    # except Exception as e:
    #     # For debugging purposes - return error details
    #     import traceback
    #     error_details = traceback.format_exc()
    #     return f"Error generating analysis: {str(e)}\n\nDetails:\n{error_details}"

@tool(return_direct=True, parse_docstring=True)
async def analyst_sentiment_analysis(symbol: str, query: str = "Provide a comprehensive analysis of analyst sentiment") -> str:
    """
    Performs a comprehensive analysis of analyst sentiment for a company including price targets,
    analyst grades, and related news.

    Args:
        symbol: Stock ticker symbol (e.g., 'AAPL' for Apple)
        query: Specific analysis question or focus area

    Returns:
        A comprehensive analysis report of analyst sentiment for the company
    """
    # Dictionary to store all collected data
    sentiment_data = {
        "symbol": symbol,
        "query": query,
        "company_overview": None,
        "price_targets": {},
        "analyst_ratings": {},
        "dividend_info": {}
    }

    # Fetch company profile
    async def fetch_company_profile():
        try:
            company_profile = await fetch_data("profile", {"symbol": symbol})
            if isinstance(company_profile, list) and len(company_profile) > 0:
                sentiment_data["company_overview"] = company_profile[0]
            else:
                sentiment_data["company_overview"] = {"error": "Failed to retrieve company profile"}
        except Exception as e:
            sentiment_data["company_overview"] = {"error": f"Error retrieving company profile: {str(e)}"}

    # Fetch price target data
    async def fetch_price_targets():
        try:
            # Create tasks for all price target data
            summary_task = fetch_data("price-target-summary", {"symbol": symbol})
            consensus_task = fetch_data("price-target-consensus", {"symbol": symbol})
            news_task = fetch_data("price-target-news", {"symbol": symbol, "limit": 50})

            # Execute all tasks concurrently
            summary, consensus, news = await asyncio.gather(summary_task, consensus_task, news_task)

            # Process results
            if isinstance(summary, list) and len(summary) > 0:
                sentiment_data["price_targets"]["summary"] = summary[0]
            else:
                sentiment_data["price_targets"]["summary"] = {"error": "Failed to retrieve price target summary"}

            if isinstance(consensus, list) and len(consensus) > 0:
                sentiment_data["price_targets"]["consensus"] = consensus[0]
            else:
                sentiment_data["price_targets"]["consensus"] = {"error": "Failed to retrieve price target consensus"}

            if isinstance(news, list):
                sentiment_data["price_targets"]["news"] = news[:50]
            else:
                sentiment_data["price_targets"]["news"] = {"error": "Failed to retrieve price target news"}

        except Exception as e:
            sentiment_data["price_targets"]["error"] = f"Error retrieving price targets: {str(e)}"

    # Fetch analyst ratings data
    async def fetch_analyst_ratings():
        try:
            # Create tasks for all analyst ratings data
            grades_task = fetch_data("grades", {"symbol": symbol})
            historical_grades_task = fetch_data("grades-historical", {"symbol": symbol, "limit": 50})
            consensus_task = fetch_data("grades-consensus", {"symbol": symbol})
            news_task = fetch_data("grades-news", {"symbol": symbol, "limit": 50})

            # Execute all tasks concurrently
            grades, historical_grades, consensus, news = await asyncio.gather(
                grades_task, historical_grades_task, consensus_task, news_task
            )

            # Process results
            if isinstance(grades, list):
                sentiment_data["analyst_ratings"]["recent_grades"] = grades[:50]
            else:
                sentiment_data["analyst_ratings"]["recent_grades"] = {"error": "Failed to retrieve recent grades"}

            if isinstance(historical_grades, list):
                sentiment_data["analyst_ratings"]["historical_grades"] = historical_grades[:50]
            else:
                sentiment_data["analyst_ratings"]["historical_grades"] = {"error": "Failed to retrieve historical grades"}

            if isinstance(consensus, list) and len(consensus) > 0:
                sentiment_data["analyst_ratings"]["consensus"] = consensus[0]
            else:
                sentiment_data["analyst_ratings"]["consensus"] = {"error": "Failed to retrieve grades consensus"}

            if isinstance(news, list):
                sentiment_data["analyst_ratings"]["news"] = news[:50]
            else:
                sentiment_data["analyst_ratings"]["news"] = {"error": "Failed to retrieve grade news"}

        except Exception as e:
            sentiment_data["analyst_ratings"]["error"] = f"Error retrieving analyst ratings: {str(e)}"

    # Fetch dividend information
    async def fetch_dividend_info():
        try:
            dividends = await fetch_data("dividends", {"symbol": symbol})
            if isinstance(dividends, list):
                sentiment_data["dividend_info"]["recent_dividends"] = dividends[:50]
            else:
                sentiment_data["dividend_info"]["recent_dividends"] = {"error": "Failed to retrieve dividend information"}
        except Exception as e:
            sentiment_data["dividend_info"]["recent_dividends"] = {"error": f"Error retrieving dividend information: {str(e)}"}

    # Execute all data fetching concurrently
    await asyncio.gather(
        fetch_company_profile(),
        fetch_price_targets(),
        fetch_analyst_ratings(),
        fetch_dividend_info()
    )

    return json.dumps(sentiment_data, indent=2)
    # # 5. Generate Comprehensive Analysis Report
    # try:
    #     # Configure OpenAI client with API key
    #     openai.api_key = settings.OPENAI_APIKEY

    #     # Define system message
    #     system_message = """
    #     You are a financial analysis expert specializing in analyst sentiment and stock ratings.
    #     Your task is to synthesize data about price targets and analyst ratings into a comprehensive,
    #     insightful report. Focus on the most meaningful patterns and trends in analyst sentiment.

    #     Structure your analysis as follows:
    #     1. Company Overview: Brief introduction to the company and its current market position.
    #     2. Price Target Analysis:
    #        - Analyze the range, consensus, and trends in price targets
    #        - Compare current price to consensus targets
    #        - Identify changes in analyst expectations over time
    #     3. Analyst Rating Analysis:
    #        - Summarize the current consensus rating
    #        - Analyze recent upgrades/downgrades and their rationale
    #        - Identify patterns in analyst opinions and potential catalysts
    #     4. News Impact Assessment:
    #        - Analyze how recent news has affected analyst sentiment
    #        - Identify key events driving changes in ratings or price targets
    #     5. Sentiment Trend Analysis:
    #        - Identify if analyst sentiment is improving, deteriorating, or mixed
    #        - Compare current sentiment to historical patterns
    #     6. Investment Implications:
    #        - Synthesize what the analyst sentiment suggests for investors
    #        - Identify potential risks and opportunities based on analyst views

    #     Always maintain a balanced, objective tone and cite specific metrics from the data provided.
    #     """

    #     # Create user content with sentiment data
    #     user_content = f"""
    #     Please analyze the following analyst sentiment data for {symbol} and address this query: {query}

    #     Data:
    #     {json.dumps(sentiment_data, indent=2)}

    #     Create a comprehensive analysis that provides valuable insights based on this data.

    #     Give as much details as possible in your analysis.
    #     """

    #     # Call OpenAI API
    #     response = openai.chat.completions.create(
    #         model="gpt-4.1-nano-2025-04-14",
    #         messages=[
    #             {"role": "system", "content": system_message},
    #             {"role": "user", "content": user_content}
    #         ]
    #     )

    #     # Extract and return the analysis report
    #     analysis_report = response.choices[0].message.content
    #     return analysis_report

    # except Exception as e:
    #     # For debugging purposes - return error details
    #     import traceback
    #     error_details = traceback.format_exc()
    #     return f"Error generating analysis: {str(e)}\n\nDetails:\n{error_details}"

@tool(return_direct=True, parse_docstring=True)
async def technical_analysis(symbol: str, query: str = "Provide a comprehensive technical analysis") -> str:
    """
    Performs a comprehensive technical analysis for a stock using various technical indicators.
    Collects 3 years of historical data but only analyzes the most recent LOOKBACK_DAYS days.

    Args:
        symbol: Stock ticker symbol (e.g., 'AAPL' for Apple)
        query: Specific analysis question or focus area

    Returns:
        A comprehensive technical analysis report based on technical indicators
    """
    import pandas as pd
    import warnings
    import numpy as np
    from datetime import datetime, timedelta
    import json
    import traceback
    import asyncio

    # Define lookback period constant
    LOOKBACK_DAYS = 15

    # Suppress warnings
    warnings.filterwarnings('ignore')

    # Dictionary to store all collected data
    technical_data = {
        "symbol": symbol,
        "query": query,
        "price_data": None,
        "technical_indicators": None,
        "analysis_period": f"Last {LOOKBACK_DAYS} days"
    }

    try:
        # Import the AllStrategies class for technical indicators
        from app.tools.workflows.Program_Files.ta_strategies_combinations_TVLibrary import AllStrategies

        # Initialize Technical Analysis Strategies
        ta_indicators = AllStrategies()

        # Calculate the start date (today - 3 years)
        today = pd.to_datetime('today').normalize()
        from_date = today - pd.DateOffset(years=3)

        # Format dates for API
        from_date_str = from_date.strftime('%Y-%m-%d')
        to_date_str = today.strftime('%Y-%m-%d')

        # Use our cached data fetching function to get historical price data
        endpoint = f"historical-price-full/{symbol}"
        params = {
            'from': from_date_str,
            'to': to_date_str
        }

        # Define additional endpoints for supplementary data that might be useful
        # These will be fetched concurrently with the historical data
        company_profile_endpoint = "profile"
        company_profile_params = {"symbol": symbol}

        # Create tasks for asyncio.gather to fetch data concurrently
        tasks = [
            fetch_data(endpoint, params),  # Historical price data
            fetch_data(company_profile_endpoint, company_profile_params)  # Company profile
        ]

        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process the results
        data = results[0]
        company_profile = results[1] if not isinstance(results[1], Exception) else None

        if isinstance(data, Exception):
            return f"Error fetching historical data for {symbol}: {str(data)}"

        # Check if 'historical' data is present
        if 'historical' not in data:
            return f"No historical data found for {symbol}."

        historical_data = data['historical']
        if not historical_data:
            return f"No historical data returned for {symbol}."

        # Convert to DataFrame
        df = pd.DataFrame(historical_data)

        # Define preprocessing function to run in a separate thread
        def preprocess_dataframe():
            df_local = df.copy()

            # Convert 'date' to datetime and rename to 'Date'
            df_local['date'] = pd.to_datetime(df_local['date'])
            df_local.rename(columns={'date': 'Date'}, inplace=True)
            df_local.sort_values('Date', inplace=True)
            df_local.reset_index(drop=True, inplace=True)

            # Rename columns to have consistent capitalization
            rename_mapping = {
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'adjClose': 'AdjClose',
                'volume': 'Volume',
                'unadjustedVolume': 'UnadjustedVolume',
                'change': 'Change',
                'changePercent': 'ChangePercent',
                'vwap': 'Vwap',
                'changeOverTime': 'ChangeOverTime',
                'label': 'Label'
            }
            df_local.rename(columns=lambda x: rename_mapping.get(x, x), inplace=True)

            # Drop 'Label' column if it exists
            if 'Label' in df_local.columns:
                df_local = df_local.drop(columns=['Label'])

            return df_local

        # Run the strategies in a separate thread to avoid blocking the event loop
        def run_strategies(df_input):
            result = ta_indicators.run_all_strategies(
                df_input,
                append=True,
                ta_indicator_value=True,
                signal_score=False,
                signal_value=True,
                signal_explanation=True
            )
            return result

        # Run preprocessing and technical analysis concurrently
        # First preprocess the dataframe
        processed_df = await asyncio.to_thread(preprocess_dataframe)

        if processed_df.empty:
            return f"No data to process for {symbol}."

        # Save a copy of original data with Date column
        original_dates = processed_df['Date'].copy()

        # Make a copy of the dataframe to prevent modifications to the original
        df_copy = processed_df.copy()

        # Apply all technical indicators
        try:
            # Run the CPU-intensive task in a thread pool
            result_df = await asyncio.to_thread(lambda: run_strategies(df_copy))

            # If result_df is None, it means df_copy was modified in-place
            if result_df is None:
                result_df = df_copy

            # Check if the Date column exists in the result DataFrame
            if 'Date' not in result_df.columns:
                # Make sure the DataFrame has the same number of rows
                if len(result_df) == len(original_dates):
                    result_df.insert(0, 'Date', original_dates.values)
                else:
                    # Use original DataFrame with only the new columns from result_df
                    orig_df = pd.DataFrame({'Date': original_dates})
                    for col in result_df.columns:
                        if col not in processed_df.columns:
                            # Only add columns that weren't in the original DataFrame
                            if len(result_df) == len(orig_df):
                                orig_df[col] = result_df[col].values

                    # Merge with original data
                    result_df = pd.concat([processed_df, orig_df.drop(columns=['Date'])], axis=1)

        except Exception as inner_e:
            error_details = traceback.format_exc()
            return f"Error calculating technical indicators: {str(inner_e)}\n\nDetails:\n{error_details}"

        # Define functions for post-processing that can run concurrently
        def prepare_lookback_data():
            # Get the last LOOKBACK_DAYS days of data for analysis
            lookback = result_df.sort_values('Date', ascending=False).head(LOOKBACK_DAYS).copy()
            lookback = lookback.sort_values('Date')  # Sort back to chronological order
            return lookback

        def prepare_price_data(lookback_df):
            # Store price data for the lookback period
            price_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'AdjClose', 'Change', 'ChangePercent']
            price_columns = [col for col in price_columns if col in lookback_df.columns]
            price_data = lookback_df[price_columns].copy()

            # Convert dates to string format for JSON serialization
            price_data['Date'] = price_data['Date'].dt.strftime('%Y-%m-%d')
            return price_data.to_dict('records')

        def process_indicators(lookback_df):
            indicators_data = {}

            # Get all column names that are technical indicators
            indicator_cols = [col for col in lookback_df.columns
                             if col not in ['Date', 'Open', 'High', 'Low', 'Close', 'Volume',
                                           'AdjClose', 'UnadjustedVolume', 'Change', 'ChangePercent',
                                           'Vwap', 'ChangeOverTime']]

            # Group by indicator type
            for col in indicator_cols:
                # Skip columns that have "Explanation" in the name - we'll handle them separately
                if "Explanation" in col:
                    continue

                # For each indicator, get its values for the lookback period
                indicator_values = lookback_df[col].values.tolist()

                # Check if there's a corresponding explanation column
                explanation_col = next((ec for ec in lookback_df.columns if ec.startswith(col) and "Explanation" in ec), None)
                explanation_values = lookback_df[explanation_col].values.tolist() if explanation_col else []

                # Add to our indicators data
                indicators_data[col] = {
                    "values": indicator_values,
                    "explanations": explanation_values
                }

            return indicators_data

        # Run the lookback data preparation
        lookback_df = await asyncio.to_thread(prepare_lookback_data)

        # Run price data and indicator processing concurrently
        price_data_task = asyncio.to_thread(lambda: prepare_price_data(lookback_df))
        indicators_data_task = asyncio.to_thread(lambda: process_indicators(lookback_df))

        # Wait for both tasks to complete
        price_data, indicators_data = await asyncio.gather(price_data_task, indicators_data_task)

        # Store data in our dictionary
        technical_data['price_data'] = price_data
        technical_data['technical_indicators'] = indicators_data

        # Add company profile if available
        if company_profile and isinstance(company_profile, list) and len(company_profile) > 0:
            technical_data['company_profile'] = company_profile[0]

        return json.dumps(technical_data, indent=2)

    except Exception as e:
        error_details = traceback.format_exc()
        return f"Error processing {symbol}: {str(e)}\n\nDetails:\n{error_details}"


@tool(return_direct=True, parse_docstring=True)
async def earnings_and_transcript_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes a company's earnings and transcripts to provide a comprehensive overview.

    This tool fetches key financial metrics (TTM), earning call transcript dates,
    and the content of the latest earning call transcript for a given stock symbol.
    It then uses a language model to synthesize this information into an analysis report
    based on the provided query.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive analysis report of the company's
        earnings and transcripts.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await earnings_and_transcript_analysis("GOOGL", "Focus on revenue trends and future outlook.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of GOOGL's earnings
        # and transcripts, focusing on revenue trends and future outlook.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "revenue_ttm": None,
        "key_metrics_ttm": None,
        "transcript_year": None,
        "transcript_quarter": None,
        "transcript_text": None
    }

    # Fetch TTM Ratios and transcript dates concurrently
    async def fetch_initial_data():
        try:
            # Create tasks for concurrent execution
            ratios_task = fetch_data("ratios-ttm", {"symbol": symbol})
            transcript_dates_task = fetch_data("earning-call-transcript-dates", {"symbol": symbol})

            # Execute tasks concurrently
            ratios_ttm, dates = await asyncio.gather(ratios_task, transcript_dates_task)

            # Process ratios data
            if isinstance(ratios_ttm, list) and len(ratios_ttm) > 0:
                company_data["revenue_ttm"] = ratios_ttm[0]
            else:
                company_data["revenue_ttm"] = {"error": "Failed to retrieve company revenue TTM metrics."}

            # Process transcript dates
            if isinstance(dates, list) and len(dates) > 0:
                date = dates[0]
                company_data["key_metrics_ttm"] = date
                company_data["transcript_quarter"] = date['quarter']
                company_data["transcript_year"] = date['fiscalYear']

                # Return the year and quarter for transcript fetching
                return date['fiscalYear'], date['quarter']
            else:
                company_data["key_metrics_ttm"] = {"error": "Failed to retrieve company key TTM metrics."}
                company_data["transcript_quarter"] = {"error": "Failed to retrieve company dates."}
                company_data["transcript_year"] = {"error": "Failed to retrieve company dates."}
                return None, None

        except Exception as e:
            company_data["revenue_ttm"] = {"error": f"Error retrieving company revenue TTM metrics: {str(e)}"}
            company_data["key_metrics_ttm"] = {"error": f"Error retrieving company key TTM metrics: {str(e)}"}
            company_data["transcript_quarter"] = {"error": f"Error retrieving company dates: {str(e)}"}
            company_data["transcript_year"] = {"error": f"Error retrieving company dates: {str(e)}"}
            return None, None

    # Fetch transcript text
    async def fetch_transcript(year, quarter):
        try:
            if year is None or quarter is None:
                company_data["transcript_text"] = {"error": "Missing year or quarter information for transcript."}
                return

            # Add parameters for transcript fetching
            params = {"symbol": symbol}

            # Fetch transcript with caching, passing year and quarter as separate parameters
            transcript_text = await fetch_data("earning-call-transcript", params, year=year, quarter=quarter)

            if isinstance(transcript_text, list) and len(transcript_text) > 0:
                transcript = transcript_text[0]
                company_data["transcript_text"] = transcript['content']
            else:
                company_data["transcript_text"] = {"error": "Failed to retrieve company transcripts."}
        except Exception as e:
            company_data["transcript_text"] = {"error": f"Error retrieving company transcripts: {str(e)}"}

    # Execute the data fetching process
    try:
        # First fetch the initial data
        year, quarter = await fetch_initial_data()

        # Then fetch the transcript if we have year and quarter
        if year is not None and quarter is not None:
            await fetch_transcript(year, quarter)

        return json.dumps(company_data, indent=2)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        return f"Error processing {symbol}: {str(e)}\n\nDetails:\n{error_details}"


@tool(return_direct=True, parse_docstring=True)
async def valuation_dcf_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Performs a Discounted Cash Flow (DCF) valuation analysis of a company.

    This tool fetches financial statements (income statement, balance sheet, cash flow statement),
    key financial ratios and metrics, and DCF valuation data for a given stock symbol.
    It then uses a language model to synthesize this information into an analysis report
    focused on the company's intrinsic value.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive DCF valuation analysis report of the company.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await valuation_dcf_analysis("MSFT", "Focus on the key assumptions driving the DCF valuation.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the DCF valuation analysis of MSFT,
        # focusing on the key assumptions.
    """
    try:
        # Dictionary to store all collected data
        company_data = {
            "symbol": symbol,
            "query": query,
            "income_statement": None,
            "balance_sheet": None,
            "cash_flow_statement": None,
            "ratios": None,
            "key_metrics_ttm": None,
            "ratios_ttm_bulk": None,
            "dcf": None,
            "levered_dcf": None,
            "price_target_summary": None,
        }

        # Create tasks for parallel execution with prioritization
        async def fetch_all_financial_data():
            # Define endpoints with prioritization and optimized timeouts
            # Group 1: Critical financial statements (higher priority, shorter timeouts)
            # Group 2: Ratios and metrics (medium priority)
            # Group 3: DCF and price targets (lower priority, can be calculated from other data)

            # Define all endpoints with their parameters and timeouts
            endpoints = {
                # Group 1: Critical financial statements (15s timeout)
                "income_statement": ("income-statement", {"symbol": symbol}, 15),
                "balance_sheet": ("balance-sheet-statement", {"symbol": symbol}, 15),
                "cash_flow": ("cash-flow-statement", {"symbol": symbol}, 15),

                # Group 2: Ratios and metrics (20s timeout)
                "ratios": ("ratios", {"symbol": symbol}, 20),
                "key_metrics_ttm": ("key-metrics-ttm", {"symbol": symbol}, 20),

                # Group 3: DCF and price targets (25s timeout)
                "ratios_ttm_bulk": ("ratios-ttm-bulk", {"symbol": symbol}, 25),
                "dcf": ("discounted-cash-flow", {"symbol": symbol}, 25),
                "levered_dcf": ("levered-discounted-cash-flow", {"symbol": symbol}, 25),
                "price_target_summary": ("price-target-summary", {"symbol": symbol}, 25)
            }

            # Process endpoints in priority groups with semaphores to control concurrency
            # This prevents overwhelming the API and potential rate limiting

            # Create semaphores for each priority group
            group1_semaphore = asyncio.Semaphore(3)  # Allow 3 concurrent requests for critical data
            group2_semaphore = asyncio.Semaphore(2)  # Allow 2 concurrent requests for medium priority
            group3_semaphore = asyncio.Semaphore(2)  # Allow 2 concurrent requests for lower priority

            # Helper function to fetch with appropriate semaphore
            async def fetch_with_semaphore(endpoint, params, timeout, semaphore, key):
                async with semaphore:
                    try:
                        # Disable caching for financial data that changes frequently
                        return await fetch_data(endpoint, params, timeout=timeout, cache_ttl=0)
                    except Exception as e:
                        return {"error": f"Error retrieving {key}: {str(e)}"}

            # Create tasks for each priority group
            tasks = []
            task_keys = []

            # Group 1: Critical financial statements
            for key in ["income_statement", "balance_sheet", "cash_flow"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group1_semaphore, key))
                task_keys.append(key)

            # Group 2: Ratios and metrics
            for key in ["ratios", "key_metrics_ttm"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group2_semaphore, key))
                task_keys.append(key)

            # Group 3: DCF and price targets
            for key in ["ratios_ttm_bulk", "dcf", "levered_dcf", "price_target_summary"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group3_semaphore, key))
                task_keys.append(key)

            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results with early filtering
            results_dict = {}
            for i, key in enumerate(task_keys):
                result = results[i]

                if isinstance(result, Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(result)}"}
                else:
                    # Filter and process data based on type
                    if isinstance(result, list):
                        # For financial statements, we only need the most recent data (first item)
                        if len(result) > 0:
                            # For bulk data, limit to reduce size
                            if key == "ratios_ttm_bulk" and len(result) > 10:
                                results_dict[key] = result[:10]
                            else:
                                results_dict[key] = result
                        else:
                            results_dict[key] = []
                    else:
                        results_dict[key] = result

            return results_dict

        # Fetch all financial data concurrently with an overall timeout
        try:
            # Set an overall timeout for the entire operation (50 seconds)
            financial_data = await asyncio.wait_for(fetch_all_financial_data(), timeout=50)
        except asyncio.TimeoutError:
            return json.dumps({
                "symbol": symbol,
                "query": query,
                "error": "The operation timed out after 50 seconds. Please try again later."
            }, indent=2)

        # Process results with optimized handling
        # Use a mapping to simplify the code and reduce repetition
        key_mapping = {
            "income_statement": "income_statement",
            "balance_sheet": "balance_sheet",
            "cash_flow": "cash_flow_statement",
            "ratios": "ratios",
            "key_metrics_ttm": "key_metrics_ttm",
            "ratios_ttm_bulk": "ratios_ttm_bulk",
            "dcf": "dcf",
            "levered_dcf": "levered_dcf",
            "price_target_summary": "price_target_summary"
        }

        # Process all results using the mapping
        for source_key, target_key in key_mapping.items():
            data = financial_data.get(source_key)

            if isinstance(data, list):
                if len(data) > 0:
                    # For most data, we only need the first (most recent) item
                    company_data[target_key] = data[0]
                else:
                    company_data[target_key] = {"info": f"No data available for {source_key}"}
            elif isinstance(data, dict) and "error" in data:
                company_data[target_key] = data
            else:
                company_data[target_key] = {"error": f"Failed to retrieve {source_key}"}

        return json.dumps(company_data, indent=2)

    except Exception as e:
        # Comprehensive error handling
        import traceback
        error_details = traceback.format_exc()
        return json.dumps({
            "symbol": symbol,
            "query": query,
            "error": f"Error in valuation_dcf_analysis: {str(e)}",
            "details": error_details
        }, indent=2)



@tool(return_direct=True, parse_docstring=True)
async def esg_sustainability_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes a company's Environmental, Social, and Governance (ESG) performance
    and sustainability initiatives.

    This tool fetches ESG ratings, disclosures, benchmark data, recent news,
    and press releases related to the company's sustainability efforts. It then
    uses a language model to synthesize this information into an analysis report
    based on the provided query.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive ESG and sustainability analysis
        report of the company.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await esg_sustainability_analysis("TSLA", "Focus on recent environmental controversies.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the ESG and sustainability analysis
        # of TSLA, focusing on recent environmental controversies.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "esg_rating": None,
        "esg_disclosures": None,
        "esg_benchmark": None,
        "news": None,
        "press_releases": None,

    }

    # Calculate date range for news (last 7 days)
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    from_date = week_ago.strftime("%Y-%m-%d")
    to_date = today.strftime("%Y-%m-%d")

    # Use the optimized fetch_data function for all API calls

    # Helper function to fetch multiple pages using the optimized fetch_data
    async def fetch_multi_page_optimized(endpoint: str, base_params: dict = None, pages=5, items_per_page=10):
        if base_params is None:
            base_params = {}

        # Add symbol to params if not present
        if "symbol" not in base_params and symbol is not None:
            base_params["symbol"] = symbol

        # Create tasks for all pages to fetch concurrently
        tasks = []
        for page in range(pages):
            params = base_params.copy()
            params["page"] = page
            params["limit"] = items_per_page
            tasks.append(fetch_data(endpoint, params))

        # Execute all page fetches concurrently
        results = await asyncio.gather(*tasks)

        # Process results
        all_items = []
        for data in results:
            if isinstance(data, list) and data:
                all_items.extend(data)
            elif isinstance(data, dict) and "error" in data:
                # If we have no results yet and got an error, return the error
                if not all_items:
                    return data
            elif not data:
                # No more data available for this page
                continue

        return all_items

    # Create tasks for parallel execution
    async def fetch_all_esg_data():
        try:
            # Create tasks for all ESG data endpoints
            tasks = {
                "esg_rating": fetch_data("esg-rating", {"symbol": symbol}),
                "esg_disclosure": fetch_data("esg-disclosures", {"symbol": symbol}),
                "esg_benchmark": fetch_data("esg-benchmark", {"symbol": symbol})
            }

            # Add news and press releases with date parameters
            base_params = {
                "from": from_date,
                "to": to_date
            }
            tasks["news"] = fetch_multi_page_optimized("news/stock-latest", base_params, pages=5, items_per_page=10)
            tasks["press_releases"] = fetch_multi_page_optimized("news/press-releases-latest", base_params, pages=5, items_per_page=10)

            # Execute all tasks concurrently
            results = {}
            for key, task in tasks.items():
                try:
                    results[key] = await task
                except Exception as e:
                    results[key] = {"error": f"Error retrieving {key}: {str(e)}"}

            return results
        except Exception as e:
            return {"error": f"Error fetching ESG data: {str(e)}"}

    # Fetch all ESG data concurrently
    esg_data = await fetch_all_esg_data()

    # Process results
    for key, data in esg_data.items():
        if key == "esg_rating":
            if isinstance(data, list) and len(data) > 0:
                company_data["esg_rating"] = data[0]
            else:
                company_data["esg_rating"] = {"error": "Failed to retrieve ESG rating"}
        elif key == "esg_disclosure":
            if isinstance(data, list) and len(data) > 0:
                company_data["esg_disclosures"] = data[0]
            else:
                company_data["esg_disclosures"] = {"error": "Failed to retrieve ESG disclosures"}
        elif key == "esg_benchmark":
            if isinstance(data, list) and len(data) > 0:
                company_data["esg_benchmark"] = data[0]
            else:
                company_data["esg_benchmark"] = {"error": "Failed to retrieve ESG benchmark"}
        elif key == "news":
            if isinstance(data, list):
                company_data["news"] = data
            else:
                company_data["news"] = {"error": "Failed to retrieve news"}
        elif key == "press_releases":
            if isinstance(data, list):
                company_data["press_releases"] = data
            else:
                company_data["press_releases"] = {"error": "Failed to retrieve press releases"}

    return json.dumps(company_data, indent=2)


@tool(return_direct=True, parse_docstring=True)
async def macro_economic_sector_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes a company's performance and valuation in the context of macro-economic,
    sector, and industry trends.

    This tool fetches company-specific information (industry, sector, exchange),
    macro-economic indicators (GDP, unemployment, inflation, treasury rates),
    and sector/industry performance and P/E ratios (current and historical).
    It then uses a language model to synthesize this information into an analysis report
    that contextualizes the company within broader economic and market trends.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive macro-economic and sector analysis
        report of the company.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await macro_economic_sector_analysis("NVDA", "Analyze how the semiconductor industry's performance impacts the company's valuation.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of NVDA within the
        # context of semiconductor industry and broader economic trends.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "symbol_industry": None,
        "symbol_sector": None,
        "symbol_exchange": None,
        "gdp": None,
        "unemployment": None,
        "inflation": None,
        "treasury_rates": None,
        "sector_performance": None,
        "historical_sector_performance": None,
        "industry_performance": None,
        "historical_industry_performance": None,
        "sector_pe": None,
        "historical_sector_pe": None,
        "industry_pe": None,
        "historical_industry_pe": None,
    }

    # Calculate date range for news (last 7 days)
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    from_date = week_ago.strftime("%Y-%m-%d")
    to_date = today.strftime("%Y-%m-%d")

    # Use the optimized fetch_data function for all API calls

    # Helper function to fetch multiple pages using the optimized fetch_data
    async def fetch_multi_page_optimized(endpoint: str, base_params: dict = None, pages=5, items_per_page=10):
        if base_params is None:
            base_params = {}

        # Add symbol to params if not present
        if "symbol" not in base_params and symbol is not None:
            base_params["symbol"] = symbol

        # Create tasks for all pages to fetch concurrently
        tasks = []
        for page in range(pages):
            params = base_params.copy()
            params["page"] = page
            params["limit"] = items_per_page
            tasks.append(fetch_data(endpoint, params))

        # Execute all page fetches concurrently
        results = await asyncio.gather(*tasks)

        # Process results
        all_items = []
        for data in results:
            if isinstance(data, list) and data:
                all_items.extend(data)
            elif isinstance(data, dict) and "error" in data:
                # If we have no results yet and got an error, return the error
                if not all_items:
                    return data
            elif not data:
                # No more data available for this page
                continue

        return all_items

    # Create tasks for parallel execution
    async def fetch_all_market_data():
        try:
            # First, get company info to extract industry and sector
            company_info = await fetch_data("search-exchange-variants", {"symbol": symbol})

            # Extract industry and sector if available
            symbol_industry = None
            symbol_sector = None
            symbol_exchange = None
            if isinstance(company_info, list) and len(company_info) > 0:
                company_data_extracted = company_info[0]
                symbol_industry = company_data_extracted.get('industry')
                symbol_sector = company_data_extracted.get('sector')
                symbol_exchange = company_data_extracted.get('exchange')

            # Create tasks for all market data endpoints
            tasks = {
                "gdp": fetch_data("economic-indicators?name=GDP"),
                "unemployment": fetch_data("economic-indicators?name=unemploymentRate"),
                "inflation": fetch_data("economic-indicators?name=inflationRate"),
                "treasury_rates": fetch_data("treasury-rates"),
                "sector_performance": fetch_data(f"sector-performance-snapshot?date={to_date}"),
                "industry_performance": fetch_data(f"industry-performance-snapshot?date={to_date}"),
                "sector_pe": fetch_data(f"sector-pe-snapshot?date={to_date}"),
                "industry_pe": fetch_data(f"industry-pe-snapshot?date={to_date}")
            }

            # Add tasks that depend on industry and sector
            if symbol_sector:
                tasks["historical_sector_performance"] = fetch_data(f"historical-sector-performance?sector={symbol_sector}")
                tasks["historical_sector_pe"] = fetch_data(f"historical-sector-pe?sector={symbol_sector}")

            if symbol_industry:
                tasks["historical_industry_performance"] = fetch_data(f"historical-industry-performance?industry={symbol_industry}")
                tasks["historical_industry_pe"] = fetch_data(f"historical-industry-pe?industry={symbol_industry}")

            # Execute all tasks concurrently
            results = {}
            for key, task in tasks.items():
                try:
                    results[key] = await task
                except Exception as e:
                    results[key] = {"error": f"Error retrieving {key}: {str(e)}"}

            # Add company info to results
            results["symbol_industry"] = symbol_industry or {"error": "Failed to retrieve company information."}
            results["symbol_sector"] = symbol_sector or {"error": "Failed to retrieve company information."}
            results["symbol_exchange"] = symbol_exchange or {"error": "Failed to retrieve company information."}

            return results
        except Exception as e:
            return {"error": f"Error fetching market data: {str(e)}"}

    # Fetch all market data concurrently
    market_data = await fetch_all_market_data()

    # Process results
    for key, data in market_data.items():
        if key in ["symbol_industry", "symbol_sector", "symbol_exchange"]:
            company_data[key] = data
        elif isinstance(data, list) and len(data) > 0:
            company_data[key] = data[0]
        elif isinstance(data, dict) and "error" in data:
            company_data[key] = data
        else:
            company_data[key] = {"error": f"Failed to retrieve {key}"}

    return json.dumps(company_data, indent=2)



    # 5. Composite Report Generation
    try:
        # Configure OpenAI client with API key
        openai.api_key = settings.OPENAI_API_KEY

        # Define system message
        system_message = """
        You are a financial analysis expert. Your task is to synthesize financial data into a comprehensive,
        insightful report. Focus on how the company compares to market trends, sector trends, and industry trends.

        Structure your analysis as follows:
        1. Macro-Economic Trends: Trends composed of macro-economic indicators such as GDP, Unemployment, Inflation, Treasury Rates, etc.
        2. Sector Trends: Trends composed of sector data, such as current and historical performance and price to earnings data.
        3. Industry Trends: Trends composed of industry data, such as current and historical performance and price to earnings data.

        Always maintain a balanced, objective tone and cite specific metrics from the data provided.
        """

        # Create user content with company data
        user_content = f"""
        Please analyze the following financial data for {symbol} and address this query: {query}

        Data:
        {json.dumps(company_data, indent=2)}

        Create a comprehensive analysis that provides valuable insights based on this data.
        """

        # Call OpenAI API
        response = openai.chat.completions.create(
            model="gpt-4.1-nano-2025-04-14",
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_content}
            ]
        )

        # Extract and return the analysis report
        analysis_report = response.choices[0].message.content
        return analysis_report

    except Exception as e:
        # For debugging purposes - return error details
        import traceback
        error_details = traceback.format_exc()
        return f"Error generating analysis: {str(e)}\n\nDetails:\n{error_details}"


@tool(return_direct=True, parse_docstring=True)
async def etf_mutual_fund_holding_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes the holdings of Exchange Traded Funds (ETFs) and Mutual Funds
    that include a specific stock, providing insights into sector and regional
    trends and the company's exposure within these investment vehicles.

    This tool fetches data on ETF and mutual fund holdings, country and sector
    weightings within these funds, and bulk ETF holder information for a given
    stock symbol. It then uses a language model to synthesize this information
    into an analysis report.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive analysis report on ETF and mutual
        fund holdings of the company.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await etf_mutual_fund_holding_analysis("NVDA", "Identify ETFs with the largest holdings of this stock.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of NVDA's presence
        # in ETFs and mutual funds, potentially highlighting those with the largest holdings.
    """
    try:
        # Dictionary to store all collected data
        company_data = {
            "symbol": symbol,
            "query": query,
            "etf_fund_holdings": None,
            "etf_mutual_fund": None,
            "country_weightings": None,
            "sector_weightings": None,
            "etf_holder_bulk": None,
        }

        # Create tasks for parallel execution with optimized timeouts
        async def fetch_all_etf_data():
            # Define all endpoints and their parameters with optimized timeouts
            # Critical data gets shorter timeouts to fail fast if unavailable
            endpoints = {
                # Critical data with shorter timeouts
                "etf_fund_holdings": ("etf/holdings", {"symbol": symbol}, 15),
                "etf_mutual_fund": ("etf/info", {"symbol": symbol}, 15),

                # Less critical data with medium timeouts
                "country_weightings": ("etf/country-weightings", {"symbol": symbol}, 20),
                "sector_weightings": ("etf/sector-weightings", {"symbol": symbol}, 20),

                # Bulk data with longer timeout
                "etf_holder_bulk": ("etf-holder-bulk?part=1", {}, 30)
            }

            # Create tasks for all endpoints with specific timeouts and no caching
            tasks = []
            task_keys = []

            for key, (endpoint, params, timeout) in endpoints.items():
                # Set cache_ttl=0 to disable caching for financial data
                tasks.append(fetch_data(endpoint, params, timeout=timeout, cache_ttl=0))
                task_keys.append(key)

            # Execute all tasks concurrently with a semaphore to limit concurrency
            # This prevents overwhelming the API and potential rate limiting
            semaphore = asyncio.Semaphore(3)  # Limit to 3 concurrent requests

            async def fetch_with_semaphore(task, key):
                async with semaphore:
                    try:
                        return await task
                    except Exception as e:
                        return {"error": f"Error retrieving {key}: {str(e)}"}

            # Create tasks with semaphore
            semaphore_tasks = [fetch_with_semaphore(task, key) for task, key in zip(tasks, task_keys)]

            # Execute all tasks concurrently
            results = await asyncio.gather(*semaphore_tasks)

            # Process results with early filtering
            results_dict = {}
            for i, key in enumerate(task_keys):
                result = results[i]

                # Early filtering to reduce data size
                if key == "etf_holder_bulk" and isinstance(result, list):
                    # For bulk data, filter to only include entries related to the symbol
                    # This significantly reduces the data size
                    filtered_data = []
                    for item in result:
                        if isinstance(item, dict) and "holdings" in item:
                            # Check if any holding contains our symbol
                            holdings = item.get("holdings", [])
                            if any(h.get("symbol") == symbol for h in holdings if isinstance(h, dict)):
                                filtered_data.append(item)
                    results_dict[key] = filtered_data[:50]  # Limit to 50 entries
                elif isinstance(result, list) and len(result) > 50:
                    # Limit large lists to 50 entries
                    results_dict[key] = result[:50]
                else:
                    results_dict[key] = result

            return results_dict

        # Fetch all ETF data concurrently with an overall timeout
        try:
            # Set an overall timeout for the entire operation (45 seconds)
            etf_data = await asyncio.wait_for(fetch_all_etf_data(), timeout=45)
        except asyncio.TimeoutError:
            return json.dumps({
                "symbol": symbol,
                "query": query,
                "error": "The operation timed out after 45 seconds. Please try again later."
            }, indent=2)

        # Process results with optimized handling
        for key, data in etf_data.items():
            if isinstance(data, list):
                if key == "etf_holder_bulk":
                    # For bulk data, keep the filtered list
                    company_data[key] = data
                elif len(data) > 0:
                    # For other list data, only store the first item
                    company_data[key] = data[0]
                else:
                    company_data[key] = {"info": f"No data available for {key}"}
            elif isinstance(data, dict) and "error" in data:
                company_data[key] = data
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}

        return json.dumps(company_data, indent=2)

    except Exception as e:
        # Comprehensive error handling
        import traceback
        error_details = traceback.format_exc()
        return json.dumps({
            "symbol": symbol,
            "query": query,
            "error": f"Error in etf_mutual_fund_holding_analysis: {str(e)}",
            "details": error_details
        }, indent=2)


@tool(return_direct=True, parse_docstring=True)
async def market_data_technical_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Performs technical analysis on market data for a given stock symbol.

    This tool fetches real-time quote, price change information, various technical
    indicators (SMA, EMA, WMA, DEMA, TEMA, RSI, Standard Deviation, Williams %R, ADX),
    and intraday historical chart data at different timeframes. It then uses a
    language model to synthesize this information into an analysis report focused
    on intraday trading signals and trend analysis.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive technical analysis report of the company's
        market data.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await market_data_technical_analysis("GOOGL", "Identify potential short-term trading opportunities based on technical indicators.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the technical analysis of GOOGL,
        # focusing on short-term trading opportunities.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "quote": None,
        "stock_price_change": None,
        "sma": None,
        "ema": None,
        "wma": None,
        "dema": None,
        "tema": None,
        "rsi": None,
        "standarddeviation": None,
        "williams": None,
        "adx": None,
        "chart_1min": None,
        "chart_5min": None,
        "chart_15min": None,
        "chart_30min": None,
        "chart_1hour": None,
        "chart_4hour": None,
    }

    # Create tasks for parallel execution
    async def fetch_all_technical_data():
        try:
            # Create tasks list for asyncio.gather
            tasks = []
            task_keys = []

            # Define common parameters for technical indicators
            tech_params = {"periodLength": 10, "timeframe": "1day"}

            # Define all endpoints and their parameters
            endpoints = {
                "quote": ("quote", {"symbol": symbol}),
                "stock_price_change": ("stock-price-change", {"symbol": symbol}),
                "sma": ("technical-indicators/sma", {"symbol": symbol, **tech_params}),
                "ema": ("technical-indicators/ema", {"symbol": symbol, **tech_params}),
                "wma": ("technical-indicators/wma", {"symbol": symbol, **tech_params}),
                "dema": ("technical-indicators/dema", {"symbol": symbol, **tech_params}),
                "tema": ("technical-indicators/tema", {"symbol": symbol, **tech_params}),
                "rsi": ("technical-indicators/rsi", {"symbol": symbol, **tech_params}),
                "standarddeviation": ("technical-indicators/standarddeviation", {"symbol": symbol, **tech_params}),
                "williams": ("technical-indicators/williams", {"symbol": symbol, **tech_params}),
                "adx": ("technical-indicators/adx", {"symbol": symbol, **tech_params}),
                "chart_1min": ("historical-chart/1min", {"symbol": symbol}),
                "chart_5min": ("historical-chart/5min", {"symbol": symbol}),
                "chart_15min": ("historical-chart/15min", {"symbol": symbol}),
                "chart_30min": ("historical-chart/30min", {"symbol": symbol}),
                "chart_1hour": ("historical-chart/1hour", {"symbol": symbol}),
                "chart_4hour": ("historical-chart/4hour", {"symbol": symbol})
            }

            # Create tasks for all endpoints
            for key, (endpoint, params) in endpoints.items():
                tasks.append(fetch_data(endpoint, params))
                task_keys.append(key)

            # Execute all tasks concurrently using asyncio.gather
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            results_dict = {}
            for i, key in enumerate(task_keys):
                if isinstance(results[i], Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(results[i])}"}
                else:
                    results_dict[key] = results[i]

            return results_dict
        except Exception as e:
            return {"error": f"Error fetching technical data: {str(e)}"}

    # Fetch all technical data concurrently
    technical_data = await fetch_all_technical_data()

    # Process results
    for key, data in technical_data.items():
        if isinstance(data, list) and len(data) > 0:
            company_data[key] = data[0]
        elif isinstance(data, dict) and "error" in data:
            company_data[key] = data
        else:
            company_data[key] = {"error": f"Failed to retrieve {key}"}

    return json.dumps(company_data, indent=2)


@tool(return_direct=True, parse_docstring=True)
async def insider_Trading_institutional_ownership_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes insider trading activity and institutional ownership for a given stock symbol
    to identify potential signals regarding company performance or sentiment shifts.

    This tool fetches the latest insider trading transactions, specific insider trading
    records for the symbol, insider trading statistics, the latest institutional ownership
     filings, and a summary of institutional ownership positions for the symbol in the
    current year and quarter. It then uses a language model to synthesize this
    information into an analysis report.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive analysis report on insider trading and
        institutional ownership.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await insider_Trading_institutional_ownership_analysis("GOOGL", "Analyze recent insider buying trends and changes in institutional ownership.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of GOOGL's insider
        # trading and institutional ownership activities.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "insider_trading_latest": None,
        "insider_trading_search": None,
        "insider_trading_statistics": None,
        "institutional_ownership_latest": None,
        "institutional_ownership_symbol_positions_summary": None,
    }

    # Calculate date range for news (last 7 days)
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    from_date = week_ago.strftime("%Y-%m-%d")
    to_date = today.strftime("%Y-%m-%d")
    year = today.year
    quarter = ((today.month - 1) // 3) + 1

    # Helper function for API calls
    async def fetch_data(endpoint: str, params: dict = None, symbol: str = None, year: int = None, quarter: int = None):

        if params is None:
            params = {}

        # Add optional parameters if they are provided
        if symbol is not None:
            params["symbol"] = symbol
        if year is not None:
            params["year"] = year
        if quarter is not None:
            params["quarter"] = quarter

        params["apikey"] = settings.FMP_API_KEY

        # Construct the base URL
        url = f"{settings.FMP_BASE_URL}/{endpoint}"

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {"error": f"API Error {response.status}: {error_text}"}

    # Helper function to fetch multiple pages
    async def fetch_multi_page(endpoint: str, base_params: dict, pages=5, items_per_page=10):
        results = []
        for page in range(pages):
            params = base_params.copy()
            params["page"] = page
            params["limit"] = items_per_page

            data = await fetch_data(endpoint, params)
            if isinstance(data, list) and data:
                results.extend(data)
            elif isinstance(data, dict) and "error" in data:
                # If error encountered, stop paginating
                if not results:  # Only return error if we have no results yet
                    return data
                break
            elif not data:
                # No more data available
                break

        return results

    # Create tasks for parallel execution
    async def fetch_all_insider_data():
        try:
            # Create tasks list for asyncio.gather
            tasks = []
            task_keys = []

            # Define all endpoints and their parameters
            endpoints = {
                "insider_trading_latest": ("insider-trading/latest", {"limit": 5}),
                "insider_trading_search": ("insider-trading/search", {"symbol": symbol}),
                "insider_trading_statistics": ("insider-trading/statistics", {"symbol": symbol}),
                "institutional_ownership_latest": ("institutional-ownership/latest", {"limit": 5}),
                "institutional_ownership_symbol_positions_summary": ("institutional-ownership/symbol-positions-summary",
                                                                    {"symbol": symbol, "year": year, "quarter": quarter})
            }

            # Create tasks for all endpoints
            for key, (endpoint, params) in endpoints.items():
                tasks.append(fetch_data(endpoint, params))
                task_keys.append(key)

            # Execute all tasks concurrently using asyncio.gather
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            results_dict = {}
            for i, key in enumerate(task_keys):
                if isinstance(results[i], Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(results[i])}"}
                else:
                    results_dict[key] = results[i]

            return results_dict
        except Exception as e:
            return {"error": f"Error fetching insider data: {str(e)}"}

    # Fetch all insider data concurrently
    insider_data = await fetch_all_insider_data()

    # Process results
    for key, data in insider_data.items():
        if key in ["insider_trading_latest", "institutional_ownership_latest"]:
            if isinstance(data, list):
                company_data[key] = data
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}
        elif isinstance(data, list) and len(data) > 0:
            company_data[key] = data[0]
        elif isinstance(data, dict) and "error" in data:
            company_data[key] = data
        else:
            company_data[key] = {"error": f"Failed to retrieve {key}"}

    return json.dumps(company_data, indent=2)


@tool(return_direct=True, parse_docstring=True)
async def dividend_strategy_income_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes dividend performance and income stability for a given stock symbol,
    focusing on metrics relevant to income-focused investors.

    This tool fetches historical dividend data, upcoming dividend calendar information,
    key financial ratios, end-of-day bulk stock data, stock price change, and a list
    of stock peers. It then uses a language model to synthesize this information
    into an analysis report.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive dividend strategy and income analysis
        report.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await dividend_strategy_income_analysis("IBM", "Assess the reliability of IBM's dividend income for long-term investors.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of IBM's dividend
        # performance and income stability.
    """
    try:
        # Dictionary to store all collected data
        company_data = {
            "symbol": symbol,
            "query": query,
            "dividends": None,
            "dividends_calendar": None,
            "ratios": None,
            "eod_bulk": None,
            "stock_price_change": None,
            "stock_peers": None,
        }

        # Calculate date range for news (last 7 days)
        today = datetime.now()
        week_ago = today - timedelta(days=7)
        from_date = week_ago.strftime("%Y-%m-%d")
        to_date = today.strftime("%Y-%m-%d")

        # Create tasks for parallel execution with prioritization
        async def fetch_all_dividend_data():
            # Define endpoints with prioritization and optimized timeouts
            # Group 1: Critical dividend data (higher priority, shorter timeouts)
            # Group 2: Supporting data (medium priority)
            # Group 3: Bulk data (lower priority, longer timeouts)

            # Define all endpoints with their parameters and timeouts
            endpoints = {
                # Group 1: Critical dividend data (15s timeout)
                "dividends": ("dividends", {"symbol": symbol}, 15),
                "dividends_calendar": ("dividends-calendar", {"from": from_date, "to": to_date}, 15),

                # Group 2: Supporting data (20s timeout)
                "ratios": ("ratios", {"symbol": symbol}, 20),
                "stock_price_change": ("stock-price-change", {"symbol": symbol}, 20),
                "stock_peers": ("stock-peers", {"symbol": symbol}, 20),

                # Group 3: Bulk data (25s timeout)
                "eod_bulk": ("eod-bulk", {"date": to_date}, 25)
            }

            # Process endpoints in priority groups with semaphores to control concurrency
            # This prevents overwhelming the API and potential rate limiting

            # Create semaphores for each priority group
            group1_semaphore = asyncio.Semaphore(2)  # Allow 2 concurrent requests for critical data
            group2_semaphore = asyncio.Semaphore(2)  # Allow 2 concurrent requests for medium priority
            group3_semaphore = asyncio.Semaphore(1)  # Allow 1 concurrent request for bulk data

            # Helper function to fetch with appropriate semaphore
            async def fetch_with_semaphore(endpoint, params, timeout, semaphore, key):
                async with semaphore:
                    try:
                        # Disable caching for financial data that changes frequently
                        return await fetch_data(endpoint, params, timeout=timeout, cache_ttl=0)
                    except Exception as e:
                        return {"error": f"Error retrieving {key}: {str(e)}"}

            # Create tasks for each priority group
            tasks = []
            task_keys = []

            # Group 1: Critical dividend data
            for key in ["dividends", "dividends_calendar"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group1_semaphore, key))
                task_keys.append(key)

            # Group 2: Supporting data
            for key in ["ratios", "stock_price_change", "stock_peers"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group2_semaphore, key))
                task_keys.append(key)

            # Group 3: Bulk data
            for key in ["eod_bulk"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group3_semaphore, key))
                task_keys.append(key)

            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results with early filtering
            results_dict = {}
            for i, key in enumerate(task_keys):
                result = results[i]

                if isinstance(result, Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(result)}"}
                else:
                    # Filter and process data based on type
                    if isinstance(result, list):
                        if key == "eod_bulk" and len(result) > 50:
                            # For bulk data, limit to reduce size
                            # Filter to only include relevant dividend-paying stocks if possible
                            filtered_data = []
                            for item in result[:200]:  # Check first 200 items
                                if isinstance(item, dict) and item.get("symbol") == symbol:
                                    filtered_data.append(item)
                                    break
                                # Also include if it has a non-zero dividend yield
                                elif isinstance(item, dict) and item.get("dividendYield", 0) > 0:
                                    filtered_data.append(item)

                            # If we found relevant items, use them; otherwise limit to first 50
                            if filtered_data:
                                results_dict[key] = filtered_data[:50]
                            else:
                                results_dict[key] = result[:50]
                        elif key == "dividends" and len(result) > 20:
                            # For historical dividends, we only need the most recent 20
                            results_dict[key] = result[:20]
                        else:
                            results_dict[key] = result
                    else:
                        results_dict[key] = result

            return results_dict

        # Fetch all dividend data concurrently with an overall timeout
        try:
            # Set an overall timeout for the entire operation (40 seconds)
            dividend_data = await asyncio.wait_for(fetch_all_dividend_data(), timeout=40)
        except asyncio.TimeoutError:
            return json.dumps({
                "symbol": symbol,
                "query": query,
                "error": "The operation timed out after 40 seconds. Please try again later."
            }, indent=2)

        # Process results with optimized handling
        for key, data in dividend_data.items():
            if isinstance(data, list):
                if key == "eod_bulk":
                    # For bulk data, keep the filtered list
                    company_data[key] = data
                elif key == "dividends" and len(data) > 0:
                    # For dividends, keep the full history as it's important for analysis
                    company_data[key] = data
                elif len(data) > 0:
                    # For other list data, only store the first item
                    company_data[key] = data[0]
                else:
                    company_data[key] = {"info": f"No data available for {key}"}
            elif isinstance(data, dict) and "error" in data:
                company_data[key] = data
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}

        return json.dumps(company_data, indent=2)

    except Exception as e:
        # Comprehensive error handling
        import traceback
        error_details = traceback.format_exc()
        return json.dumps({
            "symbol": symbol,
            "query": query,
            "error": f"Error in dividend_strategy_income_analysis: {str(e)}",
            "details": error_details
        }, indent=2)

 
@tool(return_direct=True, parse_docstring=True)
async def merger_and_acquisition_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes recent merger and acquisition (M&A) activity, potentially related to a
    given stock symbol, to assess market consolidation trends and potential investment
    opportunities in affected sectors.

    This tool fetches the latest M&A transactions, searches for M&A deals related to
    a company name (derived from the symbol), retrieves market risk premium, and
    provides a snapshot of sector performance. It then uses a language model to
    synthesize this information into an analysis report.

    Args:
        symbol: The stock ticker symbol of the company to analyze (e.g., 'AAPL').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive M&A analysis report.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await merger_and_acquisition_analysis("CRM", "Analyze recent M&A activity in the software sector and its potential impact on Salesforce.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of M&A trends in the
        # software sector and potential implications for Salesforce.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "name": None,
        "mergers_acquisitions_latest": None,
        "mergers_acquisitions_search": None,
        "market_risk_premium": None,
        "sector_performance_snapshot": None,
    }

    # Calculate date range for news (last 7 days)
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    from_date = week_ago.strftime("%Y-%m-%d")
    to_date = today.strftime("%Y-%m-%d")
    year = today.year
    quarter = ((today.month - 1) // 3) + 1

    # Helper function for API calls
    async def fetch_data(endpoint: str, params: dict = None, symbol: str = None, year: int = None, quarter: int = None):

        if params is None:
            params = {}

        # Add optional parameters if they are provided
        if symbol is not None:
            params["symbol"] = symbol
        if year is not None:
            params["year"] = year
        if quarter is not None:
            params["quarter"] = quarter

        params["apikey"] = settings.FMP_API_KEY

        # Construct the base URL
        url = f"{settings.FMP_BASE_URL}/{endpoint}"

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {"error": f"API Error {response.status}: {error_text}"}

    # Helper function to fetch multiple pages
    async def fetch_multi_page(endpoint: str, base_params: dict, pages=5, items_per_page=10):
        results = []
        for page in range(pages):
            params = base_params.copy()
            params["page"] = page
            params["limit"] = items_per_page

            data = await fetch_data(endpoint, params)
            if isinstance(data, list) and data:
                results.extend(data)
            elif isinstance(data, dict) and "error" in data:
                # If error encountered, stop paginating
                if not results:  # Only return error if we have no results yet
                    return data
                break
            elif not data:
                # No more data available
                break

        return results


    # First, get the company name
    try:
        name_data = await fetch_data("search-symbol", {"query": symbol})
        if isinstance(name_data, list) and len(name_data) > 0:
            json_data = name_data[0]
            company_name = json_data.get('name')
            company_data["name"] = company_name
        else:
            company_name = None
            company_data["name"] = {"error": "Failed to retrieve company name"}
    except Exception as e:
        company_name = None
        company_data["name"] = {"error": f"Error retrieving company name: {str(e)}"}

    # Create tasks for parallel execution
    async def fetch_all_ma_data():
        try:
            # Create tasks list for asyncio.gather
            tasks = []
            task_keys = []

            # Define base endpoints and their parameters
            endpoints = {
                "mergers_acquisitions_latest": ("mergers-acquisitions-latest", {"limit": 5}),
                "market_risk_premium": ("market-risk-premium", {}),
                "sector_performance_snapshot": ("sector-performance-snapshot", {"date": to_date})
            }

            # Add tasks that depend on company name
            if company_name:
                endpoints["mergers_acquisitions_search"] = ("mergers-acquisitions-search", {"name": company_name})

            # Create tasks for all endpoints
            for key, (endpoint, params) in endpoints.items():
                tasks.append(fetch_data(endpoint, params))
                task_keys.append(key)

            # Execute all tasks concurrently using asyncio.gather
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            results_dict = {}
            for i, key in enumerate(task_keys):
                if isinstance(results[i], Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(results[i])}"}
                else:
                    results_dict[key] = results[i]

            return results_dict
        except Exception as e:
            return {"error": f"Error fetching M&A data: {str(e)}"}

    # Fetch all M&A data concurrently
    ma_data = await fetch_all_ma_data()

    # Process results
    for key, data in ma_data.items():
        if key == "mergers_acquisitions_latest":
            if isinstance(data, list):
                company_data[key] = data
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}
        elif key == "mergers_acquisitions_search":
            if isinstance(data, list) and len(data) > 0:
                company_data[key] = data[0]
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}
        elif isinstance(data, list) and len(data) > 0:
            company_data[key] = data[0]
        elif isinstance(data, dict) and "error" in data:
            company_data[key] = data
        else:
            company_data[key] = {"error": f"Failed to retrieve {key}"}


    return json.dumps(company_data, indent=2)

 
@tool(return_direct=True, parse_docstring=True)
async def ipo_market_trends_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes Initial Public Offering (IPO) market trends, focusing on new market entrants
    and their potential impact on market dynamics.

    This tool fetches data related to IPOs, including prospectuses and disclosures, and
    analyzes key metrics and ratios of established companies to provide context. It
    then uses a language model to synthesize this information into an analysis report.

    Args:
        symbol: A symbol to contextualize the IPO analysis. It might represent a peer
                or a company affected by recent IPOs.
        query: A specific question or focus area for the analysis. Defaults to
                       "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive IPO market trends analysis report.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                           The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await ipo_market_trends_analysis("UBER", "Assess the impact of recent tech IPOs on the ride-sharing market.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of recent IPOs and their
        # potential market impact.
    """
    try:
        # Dictionary to store all collected data
        company_data = {
            "symbol": symbol,
            "query": query,
            "name": None,
            "ipos_prospectus": None,
            "ipos_disclosure": None,
            "key_metrics_ttm_bulk": None,
            "ratios_ttm_bulk": None,
        }

        # Calculate date range for news (last 7 days)
        today = datetime.now()
        week_ago = today - timedelta(days=7)
        from_date = week_ago.strftime("%Y-%m-%d")
        to_date = today.strftime("%Y-%m-%d")

        # Create tasks for parallel execution with prioritization
        async def fetch_all_ipo_data():
            # Define endpoints with prioritization and optimized timeouts
            # Group 1: Critical IPO data (higher priority, shorter timeouts)
            # Group 2: Bulk metrics data (lower priority, longer timeouts)

            # Define all endpoints with their parameters and timeouts
            endpoints = {
                # Group 1: Critical IPO data (15s timeout)
                "ipos_prospectus": ("ipos-prospectus", {"from": from_date, "to": to_date}, 15),
                "ipos_disclosure": ("ipos-disclosure", {"from": from_date, "to": to_date}, 15),

                # Group 2: Bulk metrics data (25s timeout)
                "key_metrics_ttm_bulk": ("key-metrics-ttm-bulk", {}, 25),
                "ratios_ttm_bulk": ("ratios-ttm-bulk", {}, 25)
            }

            # Process endpoints in priority groups with semaphores to control concurrency
            # This prevents overwhelming the API and potential rate limiting

            # Create semaphores for each priority group
            group1_semaphore = asyncio.Semaphore(2)  # Allow 2 concurrent requests for critical data
            group2_semaphore = asyncio.Semaphore(1)  # Allow 1 concurrent request for bulk data

            # Helper function to fetch with appropriate semaphore
            async def fetch_with_semaphore(endpoint, params, timeout, semaphore, key):
                async with semaphore:
                    try:
                        # Disable caching for financial data that changes frequently
                        return await fetch_data(endpoint, params, timeout=timeout, cache_ttl=0)
                    except Exception as e:
                        return {"error": f"Error retrieving {key}: {str(e)}"}

            # Create tasks for each priority group
            tasks = []
            task_keys = []

            # Group 1: Critical IPO data
            for key in ["ipos_prospectus", "ipos_disclosure"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group1_semaphore, key))
                task_keys.append(key)

            # Group 2: Bulk metrics data
            for key in ["key_metrics_ttm_bulk", "ratios_ttm_bulk"]:
                endpoint, params, timeout = endpoints[key]
                tasks.append(fetch_with_semaphore(endpoint, params, timeout, group2_semaphore, key))
                task_keys.append(key)

            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results with early filtering
            results_dict = {}
            for i, key in enumerate(task_keys):
                result = results[i]

                if isinstance(result, Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(result)}"}
                else:
                    # Filter and process data based on type
                    if isinstance(result, list):
                        if key in ["key_metrics_ttm_bulk", "ratios_ttm_bulk"]:
                            if len(result) > 0:
                                # For bulk data, filter to include only relevant metrics
                                # First, try to find entries related to the symbol
                                filtered_data = []
                                for item in result[:200]:  # Check first 200 items
                                    if isinstance(item, dict) and item.get("symbol") == symbol:
                                        filtered_data.append(item)
                                        break

                                # If we found relevant items, use them; otherwise limit to first 30
                                if filtered_data:
                                    results_dict[key] = filtered_data
                                else:
                                    # For bulk data, only keep the first 30 items to reduce size
                                    results_dict[key] = result[:30]
                            else:
                                results_dict[key] = []
                        else:
                            # For IPO data, keep all entries as they're all relevant
                            results_dict[key] = result
                    else:
                        results_dict[key] = result

            return results_dict

        # Fetch all IPO data concurrently with an overall timeout
        try:
            # Set an overall timeout for the entire operation (35 seconds)
            ipo_data = await asyncio.wait_for(fetch_all_ipo_data(), timeout=35)
        except asyncio.TimeoutError:
            return json.dumps({
                "symbol": symbol,
                "query": query,
                "error": "The operation timed out after 35 seconds. Please try again later."
            }, indent=2)

        # Process results with optimized handling
        for key, data in ipo_data.items():
            if isinstance(data, list):
                if len(data) > 0:
                    if key in ["ipos_prospectus", "ipos_disclosure"]:
                        # For IPO data, only store the first item (most recent)
                        company_data[key] = data[0]
                    else:
                        # For bulk data, store the filtered list
                        company_data[key] = data
                else:
                    company_data[key] = {"info": f"No data available for {key}"}
            elif isinstance(data, dict) and "error" in data:
                company_data[key] = data
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}

        return json.dumps(company_data, indent=2)

    except Exception as e:
        # Comprehensive error handling
        import traceback
        error_details = traceback.format_exc()
        return json.dumps({
            "symbol": symbol,
            "query": query,
            "error": f"Error in ipo_market_trends_analysis: {str(e)}",
            "details": error_details
        }, indent=2)

 
@tool(return_direct=True, parse_docstring=True)
async def forex_and_commodity_trading_analysis(forex_symbol: str, commodities_symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes forex and commodity trading opportunities by integrating real-time data,
    historical prices, and technical indicators to develop a potential trading strategy.

    This tool fetches live quotes, historical price data, and calculates various
    technical indicators (SMA, EMA, WMA, DEMA, TEMA, RSI, Standard Deviation, Williams %R, ADX)
    for a specified forex pair and a commodity. It then uses a language model to
    synthesize this information into a trading strategy report.

    Args:
        forex_symbol: The symbol for the forex pair (e.g., 'EURUSD').
        commodities_symbol: The symbol for the commodity (e.g., 'XAUUSD' for Gold).
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive forex and commodity trading analysis report.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await forex_and_commodity_trading_analysis("EURUSD", "XAUUSD", "Develop a short-term trading strategy based on technical indicators for both assets.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis and a potential trading
        # strategy for EURUSD and Gold.
    """
    # Dictionary to store all collected data
    company_data = {
        "forex_symbol": forex_symbol,
        "commodities_symbol": commodities_symbol,
        "query": query,
        "forex_quote": None,
        "commodities_quote": None,
        "forex_historical_price": None,
        "commodities_historical_price": None,
        "forex_sma": None,
        "forex_ema": None,
        "forex_wma": None,
        "forex_dema": None,
        "forex_tema": None,
        "forex_rsi": None,
        "forex_standarddeviation": None,
        "forex_williams": None,
        "forex_adx": None,
        "commodities_sma": None,
        "commodities_ema": None,
        "commodities_wma": None,
        "commodities_dema": None,
        "commodities_tema": None,
        "commodities_rsi": None,
        "commodities_standarddeviation": None,
        "commodities_williams": None,
        "commodities_adx": None,
    }

    # Create tasks for parallel execution
    async def fetch_all_forex_commodity_data():
        try:
            # Define common parameters for technical indicators
            tech_params = {"periodLength": 10, "timeframe": "1day"}

            # Create tasks list for asyncio.gather
            tasks = []
            task_keys = []

            # Add basic data tasks
            basic_data = {
                "forex_quote": ("quote", {"symbol": forex_symbol}),
                "commodities_quote": ("quote", {"symbol": commodities_symbol}),
                "forex_historical_price": ("historical-price-eod/full", {"symbol": forex_symbol}),
                "commodities_historical_price": ("historical-price-eod/full", {"symbol": commodities_symbol})
            }

            for key, (endpoint, params) in basic_data.items():
                tasks.append(fetch_data(endpoint, params))
                task_keys.append(key)

            # Add forex technical indicators
            forex_indicators = {
                "forex_sma": "sma",
                "forex_ema": "ema",
                "forex_wma": "wma",
                "forex_dema": "dema",
                "forex_tema": "tema",
                "forex_rsi": "rsi",
                "forex_standarddeviation": "standarddeviation",
                "forex_williams": "williams",
                "forex_adx": "adx"
            }

            for key, indicator in forex_indicators.items():
                params = tech_params.copy()
                params["symbol"] = forex_symbol
                tasks.append(fetch_data(f"technical-indicators/{indicator}", params))
                task_keys.append(key)

            # Add commodities technical indicators
            commodities_indicators = {
                "commodities_sma": "sma",
                "commodities_ema": "ema",
                "commodities_wma": "wma",
                "commodities_dema": "dema",
                "commodities_tema": "tema",
                "commodities_rsi": "rsi",
                "commodities_standarddeviation": "standarddeviation",
                "commodities_williams": "williams",
                "commodities_adx": "adx"
            }

            for key, indicator in commodities_indicators.items():
                params = tech_params.copy()
                params["symbol"] = commodities_symbol
                tasks.append(fetch_data(f"technical-indicators/{indicator}", params))
                task_keys.append(key)

            # Execute all tasks concurrently using asyncio.gather
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            results_dict = {}
            for i, key in enumerate(task_keys):
                if isinstance(results[i], Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(results[i])}"}
                else:
                    results_dict[key] = results[i]

            return results_dict
        except Exception as e:
            return {"error": f"Error fetching forex and commodity data: {str(e)}"}

    # Fetch all forex and commodity data concurrently
    trading_data = await fetch_all_forex_commodity_data()

    # Process results
    for key, data in trading_data.items():
        if isinstance(data, list) and len(data) > 0:
            company_data[key] = data[0]
        elif isinstance(data, dict) and "error" in data:
            company_data[key] = data
        else:
            company_data[key] = {"error": f"Failed to retrieve {key}"}

    return json.dumps(company_data, indent=2)

@tool(return_direct=True, parse_docstring=True)
async def crypto_market_sentiment_price_analysis(symbol: str, query: str = "Provide a comprehensive analysis") -> str:
    """
    Analyzes cryptocurrency market sentiment and price action by integrating
    real-time quotes, historical data, news, and technical indicators.

    This tool fetches current price data, historical price trends, the latest
    cryptocurrency news, and calculates various technical indicators (SMA, EMA,
    WMA, DEMA, TEMA, RSI, Standard Deviation, Williams %R, ADX) for a specified
    cryptocurrency symbol. It then uses a language model to synthesize this
    information into an analysis report.

    Args:
        symbol: The ticker symbol of the cryptocurrency (e.g., 'BTCUSD').
        query: A specific question or focus area for the analysis. Defaults to
               "Provide a comprehensive analysis".

    Returns:
        A string containing the comprehensive cryptocurrency market sentiment and
        price analysis report.

    Raises:
        Exception: If there is an error during data fetching or report generation.
                   The error details are included in the returned string.

    Example:
        >>> # Assuming settings and openai are configured
        >>> import asyncio
        >>> async def main():
        ...     report = await crypto_market_sentiment_price_analysis("ETHUSD", "Analyze the current sentiment and predict short-term price movement for Ethereum.")
        ...     print(report)
        >>> asyncio.run(main())
        # Expected output: A string containing the analysis of Ethereum's market
        # sentiment and potential short-term price movement.
    """
    # Dictionary to store all collected data
    company_data = {
        "symbol": symbol,
        "query": query,
        "quote": None,
        "batch_crypto_quotes": None,
        "historical_price_eod": None,
        "crypto_latest": None,
        "chart_1min": None,
        "chart_5min": None,
        "chart_15min": None,
        "chart_30min": None,
        "chart_1hour": None,
        "chart_4hour": None,
        "sma": None,
        "ema": None,
        "wma": None,
        "dema": None,
        "tema": None,
        "rsi": None,
        "standarddeviation": None,
        "williams": None,
        "adx": None,
    }

    # Calculate date range for news (last 7 days)
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    from_date = week_ago.strftime("%Y-%m-%d")
    to_date = today.strftime("%Y-%m-%d")

    # Create tasks for parallel execution
    async def fetch_all_crypto_data():
        try:
            # Define common parameters for technical indicators
            tech_params = {"periodLength": 10, "timeframe": "1day"}

            # Create tasks list for asyncio.gather
            tasks = []
            task_keys = []

            # Add basic data tasks
            basic_data = {
                "quote": ("quote", {"symbol": symbol}),
                "batch_crypto_quotes": ("batch-crypto-quotes", {"short": True}),
                "historical_price_eod": ("historical-price-eod/full", {"symbol": symbol, "from": from_date, "to": to_date}),
                "crypto_latest": ("news/crypto-latest", {"limit": 5})
            }

            for key, (endpoint, params) in basic_data.items():
                tasks.append(fetch_data(endpoint, params))
                task_keys.append(key)

            # Add technical indicators
            indicators = {
                "sma": "sma",
                "ema": "ema",
                "wma": "wma",
                "dema": "dema",
                "tema": "tema",
                "rsi": "rsi",
                "standarddeviation": "standarddeviation",
                "williams": "williams",
                "adx": "adx"
            }

            for key, indicator in indicators.items():
                params = tech_params.copy()
                params["symbol"] = symbol
                tasks.append(fetch_data(f"technical-indicators/{indicator}", params))
                task_keys.append(key)

            # Add chart timeframes
            timeframes = {
                "chart_1min": "1min",
                "chart_5min": "5min",
                "chart_15min": "15min",
                "chart_30min": "30min",
                "chart_1hour": "1hour",
                "chart_4hour": "4hour"
            }

            for key, timeframe in timeframes.items():
                tasks.append(fetch_data(f"historical-chart/{timeframe}", {"symbol": symbol}))
                task_keys.append(key)

            # Execute all tasks concurrently using asyncio.gather
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            results_dict = {}
            for i, key in enumerate(task_keys):
                if isinstance(results[i], Exception):
                    results_dict[key] = {"error": f"Error retrieving {key}: {str(results[i])}"}
                else:
                    results_dict[key] = results[i]

            return results_dict
        except Exception as e:
            return {"error": f"Error fetching crypto data: {str(e)}"}

    # Fetch all crypto data concurrently
    crypto_data = await fetch_all_crypto_data()

    # Process results
    for key, data in crypto_data.items():
        if key == "crypto_latest":
            if isinstance(data, list):
                company_data[key] = data
            else:
                company_data[key] = {"error": f"Failed to retrieve {key}"}
        elif isinstance(data, list) and len(data) > 0:
            company_data[key] = data[0]
        elif isinstance(data, dict) and "error" in data:
            company_data[key] = data
        else:
            company_data[key] = {"error": f"Failed to retrieve {key}"}

    return json.dumps(company_data, indent=2)

