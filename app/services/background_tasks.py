import asyncio
import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from app.logging_custom_directory.logger_custom import logger
from app.memory.mem0_helper import (
    store_mem0_memory,
    is_mem0_available
)
from app.memory.memory_manager import store_user_message

@dataclass
class MemoryTask:
    """Data class for memory storage tasks"""
    user_id: str
    chat_id: str
    messages: List[Dict[str, Any]]
    symbol: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    task_type: str = "both"  # "mem0", "existing", or "both"

class BackgroundMemoryStorage:
    """Background task manager for memory storage"""
    
    def __init__(self):
        self.memory_queue = asyncio.Queue()
        self.running = False
        self.worker_task = None
        
    async def start(self):
        """Start the background worker"""
        if not self.running:
            self.running = True
            self.worker_task = asyncio.create_task(self._worker())
            logger.info("Background memory storage worker started")
    
    async def stop(self):
        """Stop the background worker"""
        self.running = False
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
            logger.info("Background memory storage worker stopped")
    
    async def add_memory_task(self, memory_task: MemoryTask):
        """Add a memory storage task to the queue"""
        await self.memory_queue.put(memory_task)
        logger.debug(f"Added memory task to queue for user {memory_task.user_id}, chat {memory_task.chat_id}")
    
    async def _worker(self):
        """Background worker that processes memory storage tasks"""
        logger.info("Background memory storage worker is running")
        
        while self.running:
            try:
                # Wait for tasks with a timeout to allow clean shutdown
                memory_task = await asyncio.wait_for(
                    self.memory_queue.get(), 
                    timeout=1.0
                )
                
                # Process the memory task
                await self._process_memory_task(memory_task)
                
                # Mark task as done
                self.memory_queue.task_done()
                
            except asyncio.TimeoutError:
                # No task received within timeout, continue loop
                continue
            except asyncio.CancelledError:
                logger.info("Background memory worker cancelled")
                break
            except Exception as e:
                logger.error(f"Error in background memory worker: {e}")
                continue
    
    async def _process_memory_task(self, memory_task: MemoryTask):
        """Process a single memory storage task"""
        try:
            if memory_task.task_type in ["mem0", "both"]:
                # Store using mem0 if available
                if is_mem0_available():
                    success = await store_mem0_memory(
                        user_id=memory_task.user_id,
                        messages=memory_task.messages,
                        chat_id=memory_task.chat_id,
                        metadata=memory_task.metadata or {}
                    )
                    if success:
                        logger.info(f"Background: Stored conversation in mem0 for user {memory_task.user_id}, chat {memory_task.chat_id}")
                    else:
                        logger.warning(f"Background: Failed to store conversation in mem0 for user {memory_task.user_id}")
                else:
                    logger.warning("Background: Mem0 not available for storing conversation")
            
            if memory_task.task_type in ["existing", "both"]:
                # Store using existing memory system
                success = await store_user_message(
                    user_id=memory_task.user_id,
                    chat_id=memory_task.chat_id,
                    messages=memory_task.messages,
                    run_async=False  # We're already in background, run synchronously
                )
                if success:
                    logger.info(f"Background: Stored conversation in existing memory for user {memory_task.user_id}, chat {memory_task.chat_id}")
                else:
                    logger.warning(f"Background: Failed to store conversation in existing memory for user {memory_task.user_id}")
                    
        except Exception as e:
            logger.error(f"Background: Error processing memory task for user {memory_task.user_id}: {e}")

# Global background memory storage instance
background_memory_storage = BackgroundMemoryStorage()

# Convenience functions for easy usage
async def queue_memory_storage(
    user_id: str,
    chat_id: str,
    messages: List[Dict[str, Any]],
    symbol: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    task_type: str = "both"
):
    """Queue a memory storage task for background processing"""
    # Prepare metadata with current timestamp
    if metadata is None:
        metadata = {}
    
    if symbol:
        metadata["symbol"] = symbol
    
    metadata["timestamp"] = datetime.datetime.now().isoformat()
    
    memory_task = MemoryTask(
        user_id=user_id,
        chat_id=chat_id,
        messages=messages,
        symbol=symbol,
        metadata=metadata,
        task_type=task_type
    )
    
    await background_memory_storage.add_memory_task(memory_task)
    logger.debug(f"Queued memory storage task for user {user_id}, chat {chat_id}") 