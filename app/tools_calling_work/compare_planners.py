#!/usr/bin/env python3
"""
Comparison script for DS<PERSON>y planner vs Original planner
"""

import json
from typing import Dict, List, Any
from collections import defaultdict

def load_results(file_path: str) -> List[Dict[str, Any]]:
    """Load results from JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return []

def analyze_task_distribution(results: List[Dict[str, Any]]) -> Dict[str, int]:
    """Analyze distribution of task counts."""
    task_counts = defaultdict(int)
    for result in results:
        if result.get('status') == 'success':
            count = len(result.get('tasks', []))
            task_counts[count] += 1
    return dict(task_counts)

def analyze_tag_usage(results: List[Dict[str, Any]]) -> Dict[str, int]:
    """Analyze usage of different task tags."""
    tag_counts = defaultdict(int)
    for result in results:
        if result.get('status') == 'success':
            for task in result.get('tasks', []):
                tag = task.get('tag')
                if tag:
                    tag_counts[tag] += 1
    return dict(tag_counts)

def analyze_entity_usage(results: List[Dict[str, Any]]) -> Dict[str, int]:
    """Analyze entity field usage patterns."""
    entity_patterns = defaultdict(int)
    for result in results:
        if result.get('status') == 'success':
            for task in result.get('tasks', []):
                entity = task.get('entity')
                if entity is None:
                    entity_patterns['null'] += 1
                elif isinstance(entity, str):
                    entity_patterns['string'] += 1
                elif isinstance(entity, list):
                    entity_patterns['list'] += 1
                else:
                    entity_patterns['other'] += 1
    return dict(entity_patterns)

def compare_planners():
    """Compare DSPy planner with original planner results."""
    
    # Load results
    dspy_results = load_results('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/planner_dspy_results.json')
    original_results = load_results('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/planner_v2_results.json')
    
    if not dspy_results:
        print("❌ DSPy results not found")
        return
    
    if not original_results:
        print("❌ Original results not found")
        return
    
    print("🔍 PLANNER COMPARISON ANALYSIS")
    print("=" * 50)
    
    # Basic statistics
    print(f"\n📊 BASIC STATISTICS:")
    print(f"DSPy Planner:      {len(dspy_results)} queries processed")
    print(f"Original Planner:  {len(original_results)} queries processed")
    
    # Success rates
    dspy_success = len([r for r in dspy_results if r.get('status') == 'success'])
    original_success = len([r for r in original_results if 'tasks' in r])
    
    print(f"\n✅ SUCCESS RATES:")
    print(f"DSPy Planner:      {dspy_success}/{len(dspy_results)} ({dspy_success/len(dspy_results)*100:.1f}%)")
    print(f"Original Planner:  {original_success}/{len(original_results)} ({original_success/len(original_results)*100:.1f}%)")
    
    # Task count distribution
    print(f"\n📈 TASK COUNT DISTRIBUTION:")
    dspy_task_dist = analyze_task_distribution(dspy_results)
    original_task_dist = analyze_task_distribution(original_results)
    
    print("DSPy Planner:")
    for count in sorted(dspy_task_dist.keys()):
        print(f"  {count} tasks: {dspy_task_dist[count]} queries")
    
    print("Original Planner:")
    for count in sorted(original_task_dist.keys()):
        print(f"  {count} tasks: {original_task_dist[count]} queries")
    
    # Tag usage analysis
    print(f"\n🏷️  TAG USAGE ANALYSIS:")
    dspy_tags = analyze_tag_usage(dspy_results)
    original_tags = analyze_tag_usage(original_results)
    
    print("DSPy Planner:")
    for tag in sorted(dspy_tags.keys()):
        print(f"  {tag}: {dspy_tags[tag]} tasks")
    
    print("Original Planner:")
    for tag in sorted(original_tags.keys()):
        print(f"  {tag}: {original_tags[tag]} tasks")
    
    # Entity usage patterns
    print(f"\n🎯 ENTITY FIELD PATTERNS:")
    dspy_entities = analyze_entity_usage(dspy_results)
    original_entities = analyze_entity_usage(original_results)
    
    print("DSPy Planner:")
    for pattern in sorted(dspy_entities.keys()):
        print(f"  {pattern}: {dspy_entities[pattern]} tasks")
    
    print("Original Planner:")
    for pattern in sorted(original_entities.keys()):
        print(f"  {pattern}: {original_entities[pattern]} tasks")
    
    # Sample comparison for specific queries
    print(f"\n🔍 SAMPLE QUERY COMPARISON:")
    
    # Find matching queries for comparison
    for i, dspy_result in enumerate(dspy_results[:5]):  # First 5 queries
        if i < len(original_results):
            original_result = original_results[i]
            query = dspy_result.get('query', 'Unknown')
            
            dspy_task_count = len(dspy_result.get('tasks', []))
            original_task_count = len(original_result.get('tasks', []))
            
            print(f"\nQuery {i+1}: {query[:60]}...")
            print(f"  DSPy:     {dspy_task_count} tasks")
            print(f"  Original: {original_task_count} tasks")
            
            if dspy_task_count != original_task_count:
                print(f"  ⚠️  Task count difference: {dspy_task_count - original_task_count}")
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print("=" * 50)

if __name__ == "__main__":
    compare_planners()
