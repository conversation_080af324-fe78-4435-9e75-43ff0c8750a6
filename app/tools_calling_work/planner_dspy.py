import os
import json
import dspy
import time
from typing import List, Literal, Optional, Union, Tuple, TypedDict, Dict, Any
from pydantic import BaseModel, Field
from dataclasses import dataclass
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END

# Configure DSPy with OpenAI
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

# Configure DSPy LM
lm = dspy.LM('openai/gpt-4o-mini', temperature=0)
dspy.configure(lm=lm)

# Configure Lang<PERSON>hain LLM for structured output
langchain_llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

# Performance tracking
@dataclass
class PerformanceMetrics:
    decomposition_time: float = 0.0
    validation_time: float = 0.0
    parsing_time: float = 0.0
    total_time: float = 0.0
    query_complexity: str = "unknown"
    validation_skipped: bool = False

# Type definitions
Tag = Literal["collect_data", "analyze", "visualize", "summarize", "general_qa"]

class Task(BaseModel):
    id: str
    parent: Optional[Union[str, List[str]]] = None
    tag: Tag
    entity: Optional[Union[str, List[str]]] = None
    cleanTask: str
    description: str

class Plan(BaseModel):
    tasks: List[Task]

# LangChain structured output models
class QueryComplexity(BaseModel):
    """Model for query complexity classification using LLM."""
    complexity: Literal["simple", "medium", "complex"] = Field(
        description="The complexity level of the query"
    )
    confidence: float = Field(
        description="Confidence score between 0.0 and 1.0",
        ge=0.0,
        le=1.0
    )
    reasoning: str = Field(
        description="Brief explanation of why this complexity was assigned"
    )

# LangGraph State
class PlannerState(TypedDict):
    query: str
    complexity: str
    confidence: float
    tasks: List[Task]
    performance_metrics: PerformanceMetrics

# LLM-based Query Complexity Classifier
complexity_classifier_llm = langchain_llm.with_structured_output(QueryComplexity)

COMPLEXITY_CLASSIFICATION_PROMPT = """
You are an expert at analyzing query complexity for financial data analysis tasks.

Classify the given query into one of three complexity levels:

**SIMPLE**: Direct data retrieval, single metric queries, current/latest information
- Examples: "What is Apple's current stock price?", "Get Tesla's market cap", "Show me Microsoft's P/E ratio"

**MEDIUM**: Queries requiring basic analysis or comparison of 2-3 entities
- Examples: "Compare Apple and Microsoft stock prices", "What are the top 3 tech stocks?"

**COMPLEX**: Multi-step analysis, advanced calculations, trend analysis, forecasting, or queries involving many entities
- Examples: "Analyze the correlation between Apple and Microsoft stock movements over the past year", "Compare the financial performance of all FAANG stocks and predict which will perform best"

Consider these factors:
1. Number of entities/stocks mentioned
2. Type of analysis required (retrieval vs analysis vs prediction)
3. Time periods involved
4. Mathematical complexity
5. Number of steps needed to complete the task

Query to classify: "{query}"

Provide your classification with confidence score and brief reasoning.
"""

class LLMQueryComplexityClassifier:
    """LLM-based query complexity classifier using structured output."""

    def __init__(self):
        self.llm = complexity_classifier_llm

    def classify(self, query: str) -> Tuple[str, float]:
        """Classify query complexity using LLM and return complexity and confidence."""
        try:
            prompt = COMPLEXITY_CLASSIFICATION_PROMPT.format(query=query)
            result = self.llm.invoke([{"role": "user", "content": prompt}])
            return result.complexity, result.confidence
        except Exception as e:
            print(f"⚠️ LLM complexity classification failed: {e}")
            # Fallback to simple heuristics
            word_count = len(query.split())
            if word_count > 20:
                return "complex", 0.7
            elif word_count > 10:
                return "medium", 0.6
            else:
                return "simple", 0.8

# Global instances
complexity_classifier = LLMQueryComplexityClassifier()

# LangChain structured output for task planning
task_planner_llm = langchain_llm.with_structured_output(Plan)

TASK_PLANNING_PROMPT = """
You are an expert TaskDecomposer specializing in creating optimal, logical task hierarchies for complex multi-step workflows.

CORE PRINCIPLES:
- EFFICIENCY: Create separate tasks for different data types to enable parallel execution
- EXECUTION READINESS: Every task must be immediately executable with clear, unambiguous inputs
- DEPENDENCY PRECISION: Parent references must guarantee ALL required data is available
- SEMANTIC CLARITY: Use standardized entity names and complete terminology
- DATA SEPARATION: Create separate tasks for different metrics/data types even for same companies
- FINANCE COVERAGE: ALL finance-related queries must generate at least one task

DEFAULT STOCK HANDLING:
- If no specific stock name or symbol is mentioned in the query, use AAPL (Apple Inc.) as the default
- Always use official stock symbols in entity fields: AAPL, MSFT, GOOGL, AMZN, TSLA, etc.

CRITICAL DEPENDENCY RULES:
- Create SEPARATE tasks for different data types
- For superlative queries ("top N", "largest"): Always start with identification task
- Multi-parent dependencies: Use ["t1", "t2", "t3"] format
- Entity fields: Use actual identifiers, NEVER task references like "$t1"

TASK TAGGING PRECISION:
- collect_data: Raw information retrieval from external sources
- analyze: Mathematical operations, calculations, statistical analysis
- visualize: Chart creation, graph generation, visual representations
- summarize: Written synthesis, report generation, findings description
- general_qa: Subjective judgments, recommendations, opinions

IMPOSSIBLE QUERIES → RETURN EMPTY TASK LIST []:
- Pure greetings without actionable requests and Requests completely outside financial/data analysis domain

Query to decompose: "{query}"

Generate a structured plan with tasks that follow the schema exactly.
"""

# DSPy Signatures with Structured Output (keeping for backward compatibility)
class TaskDecomposition(dspy.Signature):
    """Expert task decomposer that creates optimal, logical task hierarchies for complex multi-step workflows."""
    query: str = dspy.InputField(desc="User query to decompose into tasks")
    tasks: List[Task] = dspy.OutputField(desc="List of Task objects following the schema")

class SimpleTaskDecomposition(dspy.Signature):
    """Simple task decomposer for straightforward queries requiring minimal processing."""
    query: str = dspy.InputField(desc="Simple user query")
    tasks: List[Task] = dspy.OutputField(desc="List of Task objects (typically 1-2 tasks)")

class TaskValidation(dspy.Signature):
    """Validate and refine task plans to ensure they follow all rules and patterns."""
    query: str = dspy.InputField(desc="Original user query")
    tasks: List[Task] = dspy.InputField(desc="List of tasks to validate")
    validation_reasoning: str = dspy.OutputField(desc="Analysis of task plan quality and needed improvements")
    refined_tasks: List[Task] = dspy.OutputField(desc="Improved list of tasks with corrections applied")

# LangGraph Nodes
def classify_complexity_node(state: PlannerState) -> Dict[str, Any]:
    """LangGraph node for classifying query complexity using LLM."""
    complexity, confidence = complexity_classifier.classify(state["query"])
    return {
        "complexity": complexity,
        "confidence": confidence
    }

def plan_tasks_langchain_node(state: PlannerState) -> Dict[str, Any]:
    """LangGraph node for task planning using LangChain structured output."""
    try:
        prompt = TASK_PLANNING_PROMPT.format(query=state["query"])
        result = task_planner_llm.invoke([{"role": "user", "content": prompt}])
        return {"tasks": result.tasks}
    except Exception as e:
        print(f"⚠️ LangChain task planning failed: {e}")
        # Fallback to empty task list
        return {"tasks": []}

def plan_tasks_dspy_node(state: PlannerState) -> Dict[str, Any]:
    """LangGraph node for task planning using DSPy (fallback)."""
    try:
        # Use DSPy as fallback
        planner = DSPyPlanner()
        result = planner(query=state["query"])
        tasks = result.tasks if hasattr(result, 'tasks') else []
        return {"tasks": tasks}
    except Exception as e:
        print(f"⚠️ DSPy task planning failed: {e}")
        return {"tasks": []}

# Enhanced DSPy Module with Adaptive Complexity Handling
class DSPyPlanner(dspy.Module):
    def __init__(self):
        # Complex query processing
        self.complex_decompose = dspy.ChainOfThought(TaskDecomposition)
        self.validate = dspy.ChainOfThought(TaskValidation)

        # Simple query processing (faster)
        self.simple_decompose = dspy.Predict(SimpleTaskDecomposition)

        # Performance tracking
        self.performance_metrics = PerformanceMetrics()

    def forward(self, query: str):
        start_time = time.time()

        # Classify query complexity
        complexity, confidence = complexity_classifier.classify(query)
        self.performance_metrics.query_complexity = f"{complexity} ({confidence:.2f})"

        # Choose processing path based on complexity
        decomposition_start = time.time()

        if complexity == "simple":
            print(f"🚀 Using simple processing for query (confidence: {confidence:.2f})")
            decomposition = self.simple_decompose(query=query)
            tasks = decomposition.tasks if hasattr(decomposition, 'tasks') else []
        else:
            print(f"🧠 Using complex processing for query (confidence: {confidence:.2f})")
            decomposition = self.complex_decompose(query=query)
            tasks = decomposition.tasks if hasattr(decomposition, 'tasks') else []

        self.performance_metrics.decomposition_time = time.time() - decomposition_start

        # Conditional validation based on complexity and initial result quality
        validation_start = time.time()
        validation_needed = self._should_validate(tasks, complexity, confidence)

        if validation_needed:
            print("🔍 Running validation and refinement...")
            validation = self.validate(query=query, tasks=tasks)
            final_tasks = validation.refined_tasks if hasattr(validation, 'refined_tasks') else tasks
            validation_reasoning = getattr(validation, 'validation_reasoning', "")
        else:
            print("⚡ Skipping validation for performance optimization")
            final_tasks = tasks
            validation_reasoning = "Validation skipped - high confidence result"
            self.performance_metrics.validation_skipped = True

        self.performance_metrics.validation_time = time.time() - validation_start
        self.performance_metrics.total_time = time.time() - start_time

        return dspy.Prediction(
            tasks=final_tasks,
            validation_reasoning=validation_reasoning,
            performance_metrics=self.performance_metrics
        )

    def _should_validate(self, tasks: List[Task], complexity: str, confidence: float) -> bool:
        """Determine if validation is needed based on complexity and initial quality."""
        # Always validate complex queries
        if complexity == "complex":
            return True

        # Skip validation for simple queries with high confidence
        if complexity == "simple" and confidence > 0.8:
            return False

        # Validate if tasks seem problematic
        if not tasks or len(tasks) > 5:
            return True

        # Check for basic task quality issues
        for task in tasks:
            if not hasattr(task, 'id') or not hasattr(task, 'tag') or not hasattr(task, 'cleanTask'):
                return True

        return False

# Initialize the planner
planner = DSPyPlanner()

def parse_tasks_from_json(tasks_json: str) -> List[Task]:
    """Legacy function for backward compatibility - Parse tasks from JSON string."""
    try:
        tasks_data = json.loads(tasks_json)
        if not isinstance(tasks_data, list):
            return []

        tasks = []
        for task_data in tasks_data:
            if isinstance(task_data, dict) and all(key in task_data for key in ['id', 'tag', 'cleanTask', 'description']):
                # Handle parent field - convert empty lists to None
                parent = task_data.get('parent')
                if isinstance(parent, list) and len(parent) == 0:
                    parent = None

                task = Task(
                    id=task_data['id'],
                    parent=parent,
                    tag=task_data['tag'],
                    entity=task_data.get('entity'),
                    cleanTask=task_data['cleanTask'],
                    description=task_data['description']
                )
                tasks.append(task)
        return tasks
    except (json.JSONDecodeError, KeyError, TypeError):
        return []

def get_plan(user_query: str, show_performance: bool = False) -> List[dict]:
    """Enhanced get_plan function with performance tracking.

    Args:
        user_query: The user's query to process
        show_performance: Whether to display performance metrics

    Returns:
        List of task dictionaries
    """
    try:
        parsing_start = time.time()

        # Get prediction from DSPy planner
        prediction = planner(query=user_query)

        # Extract tasks (now directly from structured output)
        tasks = prediction.tasks if hasattr(prediction, 'tasks') else []

        # Handle both Task objects and dict objects for backward compatibility
        if tasks and isinstance(tasks[0], Task):
            task_dicts = [task.model_dump() for task in tasks]
        else:
            task_dicts = tasks if isinstance(tasks, list) else []

        parsing_time = time.time() - parsing_start

        # Display performance metrics if requested
        if show_performance and hasattr(prediction, 'performance_metrics'):
            metrics = prediction.performance_metrics
            print(f"\n📊 PERFORMANCE METRICS:")
            print(f"   Query Complexity: {metrics.query_complexity}")
            print(f"   Decomposition: {metrics.decomposition_time:.3f}s")
            print(f"   Validation: {metrics.validation_time:.3f}s {'(skipped)' if metrics.validation_skipped else ''}")
            print(f"   Parsing: {parsing_time:.3f}s")
            print(f"   Total Time: {metrics.total_time:.3f}s")

        return task_dicts

    except Exception as e:
        print(f"❌ Error in DSPy planner: {e}")
        return []

# Enhanced evaluation metric for plan quality
def evaluate_plan_quality(example, pred):
    """Evaluate the quality of a generated plan with structured output support."""
    try:
        # Handle both old JSON format and new structured format
        if hasattr(pred, 'tasks') and isinstance(pred.tasks, list):
            tasks = pred.tasks
        elif hasattr(pred, 'tasks_json'):
            tasks = parse_tasks_from_json(pred.tasks_json)
        else:
            return 0.0

        if not tasks:
            return 0.0

        score = 0.0

        # Check if finance queries generate tasks
        finance_keywords = ['stock', 'price', 'market', 'financial', 'revenue', 'earnings', 'dividend', 'investment']
        is_finance_query = any(keyword in example.query.lower() for keyword in finance_keywords)

        if is_finance_query and len(tasks) > 0:
            score += 0.3
        elif not is_finance_query and len(tasks) == 0:
            score += 0.3

        # Check task structure quality
        for task in tasks:
            # Handle both Task objects and dict objects
            task_id = task.id if hasattr(task, 'id') else task.get('id', '')
            task_tag = task.tag if hasattr(task, 'tag') else task.get('tag', '')
            task_entity = task.entity if hasattr(task, 'entity') else task.get('entity')
            task_parent = task.parent if hasattr(task, 'parent') else task.get('parent')

            # Valid task ID format
            if task_id.startswith('t') and task_id[1:].isdigit():
                score += 0.1

            # Appropriate tag usage
            if task_tag in ['collect_data', 'analyze', 'visualize', 'summarize', 'general_qa']:
                score += 0.1

            # Entity field contains actual identifiers, not task references
            if task_entity and not (isinstance(task_entity, str) and task_entity.startswith('$t')):
                score += 0.1

        # Dependency validation
        task_ids = set()
        for task in tasks:
            task_id = task.id if hasattr(task, 'id') else task.get('id', '')
            task_ids.add(task_id)

        for task in tasks:
            task_parent = task.parent if hasattr(task, 'parent') else task.get('parent')
            if task_parent:
                if isinstance(task_parent, list):
                    if all(pid in task_ids for pid in task_parent):
                        score += 0.1
                elif task_parent in task_ids:
                    score += 0.1

        return min(score, 1.0)

    except Exception as e:
        print(f"⚠️ Evaluation error: {e}")
        return 0.0

# Training examples for optimization
def create_training_examples():
    """Create training examples from train_demo_queries.json only."""
    try:
        # Load training data from train_demo_queries.json only
        queries_file = '/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/train_demo_queries.json'

        try:
            with open(queries_file, 'r') as f:
                demo_queries = json.load(f)
        except FileNotFoundError:
            print("❌ train_demo_queries.json not found")
            return []

        training_examples = []
        for query in demo_queries:
            # Create simple training examples without expected results
            # DSPy will learn from the optimization process
            example = dspy.Example(
                query=query
            ).with_inputs('query')

            training_examples.append(example)

        print(f"✅ Created {len(training_examples)} training examples")
        return training_examples

    except Exception as e:
        print(f"❌ Error creating training examples: {e}")
        return []

# Enhanced optimization function
def optimize_planner():
    """Optimize the DSPy planner using training examples."""
    global planner

    try:
        print("🚀 Starting planner optimization...")
        optimization_start = time.time()

        training_examples = create_training_examples()
        if not training_examples:
            print("❌ No training examples available for optimization")
            return planner

        print(f"🧠 Optimizing planner with {len(training_examples)} examples...")

        # Use BootstrapFewShot optimizer with enhanced settings
        optimizer = dspy.BootstrapFewShot(
            metric=evaluate_plan_quality,
            max_bootstrapped_demos=5,
            max_labeled_demos=3,
            max_rounds=2
        )

        # Create a fresh planner for optimization
        fresh_planner = DSPyPlanner()
        optimized_planner = optimizer.compile(fresh_planner, trainset=training_examples)

        optimization_time = time.time() - optimization_start
        print(f"✅ Planner optimization completed in {optimization_time:.2f}s!")

        planner = optimized_planner
        print("💾 Optimized planner loaded successfully")

        return optimized_planner

    except Exception as e:
        print(f"❌ Error during optimization: {e}")
        return planner



if __name__ == "__main__":
    print("🚀 Enhanced DSPy Planner with Performance Optimizations")
    print("=" * 60)

    # Load demo queries from JSON file
    try:
        with open('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/demo_queries.json', 'r', encoding='utf-8') as f:
            all_demo_queries = json.load(f)
            # Skip first 20 entries
            demo_queries = all_demo_queries[20:]
    except FileNotFoundError:
        print("❌ demo_queries.json not found. Please create this file with your demo queries.")
        demo_queries = []

    if not demo_queries:
        print("❌ No demo queries available. Exiting.")
        exit(1)


    print(f"\nLoaded {len(demo_queries)} demo queries for processing (skipped first 20)")

    use_optimizer = input("\nDo you want to optimize the planner first? (y/n): ").strip().lower()

    if use_optimizer == 'y':
        print("\n🧠 Starting planner optimization...")
        planner = optimize_planner()

    # Process queries with enhanced features and timing
    results = []
    total_start_time = time.time()

    # Track cumulative step times
    total_decomposition_time = 0.0
    total_validation_time = 0.0
    total_parsing_time = 0.0

    print(f"\n🔄 Processing {len(demo_queries)} queries with detailed timing...")

    for i, q in enumerate(demo_queries, 1):
        print(f"\n=== Processing Demo {i}: {q[:60]}{'...' if len(q) > 60 else ''}")

        try:
            # Track individual query timing
            query_start_time = time.time()

            # Get prediction with detailed timing
            prediction = planner(query=q)

            # Extract tasks
            tasks = prediction.tasks if hasattr(prediction, 'tasks') else []
            parsing_start = time.time()
            task_dicts = [task.model_dump() for task in tasks] if tasks and isinstance(tasks[0], Task) else tasks
            parsing_time = time.time() - parsing_start

            query_time = time.time() - query_start_time

            # Extract detailed metrics if available
            decomposition_time = 0.0
            validation_time = 0.0
            validation_skipped = False

            if hasattr(prediction, 'performance_metrics'):
                metrics = prediction.performance_metrics
                decomposition_time = metrics.decomposition_time
                validation_time = metrics.validation_time
                validation_skipped = metrics.validation_skipped

                # Add to cumulative totals
                total_decomposition_time += decomposition_time
                total_validation_time += validation_time
                total_parsing_time += parsing_time

            query_result = {
                "demo_number": i,
                "query": q,
                "tasks": task_dicts,
                "status": "success"
            }

                # "task_count": len(task_dicts),
                # "processing_time": query_time,
                # "decomposition_time": decomposition_time,
                # "validation_time": validation_time,
                # "parsing_time": parsing_time,
                # "validation_skipped": validation_skipped
            results.append(query_result)

            print(f"  📊 Step Timing:")
            print(f"    Decomposition: {decomposition_time:.3f}s")
            print(f"    Validation: {validation_time:.3f}s {'(skipped)' if validation_skipped else ''}")
            print(f"    Parsing: {parsing_time:.3f}s")
            print(f"    Total: {query_time:.3f}s")
            print(f"  ✅ Success - Generated {len(task_dicts)} tasks")

        except Exception as e:
            query_result = {
                "demo_number": i,
                "query": q,
                "tasks": [],
                "status": "error",
                "error": str(e)
            }
            results.append(query_result)
            print(f"  ❌ Planner error: {e}")

    total_time = time.time() - total_start_time

    output_file = f"/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/planner_dspy_results.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary statistics
    successful_queries = [r for r in results if r['status'] == 'success']
    total_tasks = sum(r['task_count'] for r in successful_queries)
    avg_tasks = total_tasks / len(successful_queries) if successful_queries else 0
    avg_processing_time = sum(r['processing_time'] for r in successful_queries) / len(successful_queries) if successful_queries else 0

    # Calculate average step times
    avg_decomposition_time = total_decomposition_time / len(successful_queries) if successful_queries else 0
    avg_validation_time = total_validation_time / len(successful_queries) if successful_queries else 0
    avg_parsing_time = total_parsing_time / len(successful_queries) if successful_queries else 0

    # Count validation skips
    validation_skips = sum(1 for r in successful_queries if r.get('validation_skipped', False))

    print(f"\n📊 DETAILED EXECUTION SUMMARY:")
    print(f"   Total Processing Time: {total_time:.2f}s")
    print(f"   Successful Queries: {len(successful_queries)}/{len(demo_queries)}")
    print(f"   Total Tasks Generated: {total_tasks}")
    print(f"   Average Tasks per Query: {avg_tasks:.1f}")
    print(f"\n⏱️  STEP TIMING AVERAGES:")
    print(f"   Average Decomposition Time: {avg_decomposition_time:.3f}s")
    print(f"   Average Validation Time: {avg_validation_time:.3f}s")
    print(f"   Average Parsing Time: {avg_parsing_time:.3f}s")
    print(f"   Average Total Time per Query: {avg_processing_time:.3f}s")
    print(f"   Validation Skipped: {validation_skips}/{len(successful_queries)} queries")
    print(f"\n💾 Results saved to: {output_file}")

    print(f"\n✅ Processing complete!")
