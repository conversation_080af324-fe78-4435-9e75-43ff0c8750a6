#!/usr/bin/env python3
"""
Test script for enhanced DSPy planner with all new features
"""

import sys
import time
from pathlib import Path

# Add the project root to the path
sys.path.append('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt')

from app.tools_calling_work.planner_dspy import (
    get_plan, 
    get_plan_with_symbols,
    symbol_extractor,
    complexity_classifier,
    benchmark_performance,
    optimize_planner,
    save_optimized_model,
    load_optimized_model
)

def test_symbol_extraction():
    """Test the symbol extraction functionality."""
    print("🔍 TESTING SYMBOL EXTRACTION")
    print("=" * 50)
    
    test_queries = [
        "What is Apple's current stock price?",
        "Compare AAPL and MSFT performance",
        "Analyze the technology sector trends",
        "Get Tesla and Amazon quarterly results",
        "Show me S&P 500 constituents"
    ]
    
    for query in test_queries:
        symbols = symbol_extractor.extract_symbols(query)
        print(f"\nQuery: {query}")
        print(f"Symbols: {symbols['symbols']}")
        print(f"Sectors: {symbols['sectors']}")

def test_complexity_classification():
    """Test the query complexity classifier."""
    print("\n🧠 TESTING COMPLEXITY CLASSIFICATION")
    print("=" * 50)
    
    test_queries = [
        ("What is Apple's stock price?", "simple"),
        ("Compare Apple and Microsoft stock performance over the last year and analyze trends", "complex"),
        ("Get current inflation rate", "simple"),
        ("Analyze correlation between tech stocks and market volatility during downturns", "complex"),
        ("List NASDAQ 100 companies", "simple")
    ]
    
    for query, expected in test_queries:
        complexity, confidence = complexity_classifier.classify(query)
        status = "✅" if complexity == expected else "❌"
        print(f"{status} Query: {query[:50]}...")
        print(f"   Classified: {complexity} (confidence: {confidence:.2f})")
        print(f"   Expected: {expected}")

def test_enhanced_get_plan():
    """Test the enhanced get_plan function with performance tracking."""
    print("\n⚡ TESTING ENHANCED GET_PLAN")
    print("=" * 50)
    
    test_queries = [
        "What is Tesla's current stock price?",  # Simple
        "Compare Apple, Microsoft, and Google stock performance and recommend best investment",  # Complex
        "Hello there!",  # Non-actionable
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: {query}")
        print("-" * 40)
        
        # Test with performance tracking
        start_time = time.time()
        tasks = get_plan(query, show_performance=True)
        end_time = time.time()
        
        print(f"📊 Results: {len(tasks)} tasks generated in {end_time - start_time:.3f}s")
        
        for i, task in enumerate(tasks, 1):
            print(f"  Task {i}: {task['cleanTask']}")

def test_backward_compatibility():
    """Test backward compatibility functions."""
    print("\n🔄 TESTING BACKWARD COMPATIBILITY")
    print("=" * 50)
    
    query = "Compare Apple and Microsoft stocks"
    symbols = ["AAPL", "MSFT"]
    sectors = ["Technology"]
    
    # Test old function signature
    tasks = get_plan_with_symbols(query, symbols, sectors)
    print(f"✅ get_plan_with_symbols: {len(tasks)} tasks generated")
    
    # Test new function signature
    tasks2 = get_plan(query)
    print(f"✅ get_plan (auto-extract): {len(tasks2)} tasks generated")

def test_model_persistence():
    """Test model saving and loading functionality."""
    print("\n💾 TESTING MODEL PERSISTENCE")
    print("=" * 50)
    
    # Test saving a dummy model (we'll use the current planner)
    from app.tools_calling_work.planner_dspy import planner
    
    test_path = Path("app/tools_calling_work/model_cache/test_model.pkl")
    
    # Test saving
    success = save_optimized_model(planner, test_path)
    print(f"✅ Model saving: {'Success' if success else 'Failed'}")
    
    # Test loading
    loaded_model = load_optimized_model(test_path)
    print(f"✅ Model loading: {'Success' if loaded_model else 'Failed'}")
    
    # Cleanup
    if test_path.exists():
        test_path.unlink()
        print("🧹 Test file cleaned up")

def test_performance_comparison():
    """Test performance comparison between simple and complex processing."""
    print("\n🏁 TESTING PERFORMANCE COMPARISON")
    print("=" * 50)
    
    simple_queries = [
        "What is Apple's stock price?",
        "Get Tesla's market cap",
        "Show IBM revenue"
    ]
    
    complex_queries = [
        "Compare Apple, Microsoft, and Google performance and analyze trends",
        "Analyze correlation between tech stocks and market volatility",
        "Find top 5 performing stocks and create investment recommendations"
    ]
    
    print("Simple queries:")
    simple_times = []
    for query in simple_queries:
        start = time.time()
        tasks = get_plan(query)
        end = time.time()
        simple_times.append(end - start)
        print(f"  {query[:40]}... -> {len(tasks)} tasks in {end - start:.3f}s")
    
    print("\nComplex queries:")
    complex_times = []
    for query in complex_queries:
        start = time.time()
        tasks = get_plan(query)
        end = time.time()
        complex_times.append(end - start)
        print(f"  {query[:40]}... -> {len(tasks)} tasks in {end - start:.3f}s")
    
    avg_simple = sum(simple_times) / len(simple_times)
    avg_complex = sum(complex_times) / len(complex_times)
    
    print(f"\n📊 Performance Summary:")
    print(f"   Average simple query time: {avg_simple:.3f}s")
    print(f"   Average complex query time: {avg_complex:.3f}s")
    print(f"   Performance ratio: {avg_complex/avg_simple:.1f}x")

def run_all_tests():
    """Run all test functions."""
    print("🚀 ENHANCED DSPY PLANNER - COMPREHENSIVE TESTING")
    print("=" * 60)
    
    try:
        test_symbol_extraction()
        test_complexity_classification()
        test_enhanced_get_plan()
        test_backward_compatibility()
        test_model_persistence()
        test_performance_comparison()
        
        print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
