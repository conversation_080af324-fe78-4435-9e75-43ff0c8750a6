import os 
import json
import dspy
from typing import List, Literal, Optional, Union, TypedDict, Dict, Any
from pydantic import BaseModel, Field
from langgraph.graph import StateGraph, START, END
from dspy.evaluate import Evaluate
from dspy.teleprompt import BootstrapFewShotWithRandomSearch, MIPROv2

# Set up environment
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

# Configure DSPy
lm = dspy.LM('openai/gpt-4o-mini', temperature=0)
dspy.configure(lm=lm)

Tag = Literal["collect_data", "analyze", "visualize", "summarize", "general_qa"]

class Task(BaseModel):
    id: str
    parent: Optional[Union[str, List[str]]] = None
    tag: Tag
    entity: Optional[Union[str, List[str]]] = None
    cleanTask: str
    description: str

class Plan(BaseModel):
    tasks: List[Task]

class PlannerState(TypedDict):
    query: str
    tasks: List[Task]

# ============================================================================
# STAGE 1: DSPy PROGRAMMING - Define Signatures and Modules
# ============================================================================

class TaskPlanningSignature(dspy.Signature):
    """Generate a structured task plan for financial analysis queries.
    
    CORE PRINCIPLES:
    • EFFICIENCY: Create separate tasks for different data types to enable parallel execution
    • EXECUTION READINESS: Every task must be immediately executable with clear, unambiguous inputs
    • DEPENDENCY PRECISION: Parent references must guarantee ALL required data is available
    • SEMANTIC CLARITY: Use standardized entity names and complete terminology
    • DATA SEPARATION: Create separate tasks for different metrics/data types even for same companies
    • FINANCE COVERAGE: ALL finance-related queries must generate at least one task

    DEFAULT STOCK HANDLING:
    • If no specific stock name or symbol is mentioned, use AAPL (Apple Inc.) as the default
    • Always use official stock symbols in entity fields: AAPL, MSFT, GOOGL, AMZN, TSLA, etc.

    TASK TAGGING:
    • collect_data: Raw information retrieval from external sources
    • analyze: Mathematical operations, calculations, statistical analysis
    • visualize: Chart creation, graph generation, visual representations
    • summarize: Written synthesis, report generation, findings description
    • general_qa: Subjective judgments, recommendations, opinions

    CRITICAL DEPENDENCY RULES:
    • Create SEPARATE tasks for different data types
    • For superlative queries ("top N", "largest"): Always start with identification task
    • Multi-parent dependencies: Use ["t1", "t2", "t3"] format
    • Entity fields: Use actual identifiers, NEVER task references like "$t1"

    Return ONLY valid JSON array of task objects.
    """
    
    query: str = dspy.InputField(desc="User's financial analysis query")
    plan_json: str = dspy.OutputField(desc="JSON array of task objects with id, parent, tag, entity, cleanTask, and description fields")

class TaskValidationSignature(dspy.Signature):
    """Validate a task plan for correctness and completeness."""
    
    query: str = dspy.InputField(desc="Original user query")
    plan_json: str = dspy.InputField(desc="Generated task plan as JSON")
    is_valid: bool = dspy.OutputField(desc="Whether the plan is valid and complete")
    feedback: str = dspy.OutputField(desc="Specific feedback on issues or validation results")

class TaskRefinementSignature(dspy.Signature):
    """Refine a task plan based on validation feedback."""
    
    query: str = dspy.InputField(desc="Original user query")
    plan_json: str = dspy.InputField(desc="Current task plan as JSON")
    feedback: str = dspy.InputField(desc="Validation feedback")
    refined_plan_json: str = dspy.OutputField(desc="Improved task plan as JSON")

class DSPyTaskPlanner(dspy.Module):
    """Main DSPy module for task planning with validation and refinement."""
    
    def __init__(self):
        super().__init__()
        self.task_generator = dspy.ChainOfThought(TaskPlanningSignature)
        self.task_validator = dspy.ChainOfThought(TaskValidationSignature)
        self.task_refiner = dspy.ChainOfThought(TaskRefinementSignature)
    
    def forward(self, query: str) -> dspy.Prediction:
        # Step 1: Generate initial plan
        plan_response = self.task_generator(query=query)
        
        # Step 2: Validate the plan
        validation_response = self.task_validator(
            query=query, 
            plan_json=plan_response.plan_json
        )
        
        # Step 3: Refine if needed
        if not validation_response.is_valid:
            refined_response = self.task_refiner(
                query=query,
                plan_json=plan_response.plan_json,
                feedback=validation_response.feedback
            )
            final_plan = refined_response.refined_plan_json
        else:
            final_plan = plan_response.plan_json
        
        return dspy.Prediction(
            plan_json=final_plan,
            validation_feedback=validation_response.feedback,
            is_valid=validation_response.is_valid
        )

# ============================================================================
# STAGE 2: DSPy EVALUATION - Dataset and Metrics
# ============================================================================

def create_evaluation_dataset():
    """Create a dataset for evaluating task planning quality."""
    
    # Sample training data with query-plan pairs
    training_examples = [
        dspy.Example(
            query="Compare the price to earnings ratios of Apple and Microsoft",
            expected_plan=json.dumps([
                {
                    "id": "t1",
                    "parent": None,
                    "tag": "collect_data",
                    "entity": ["AAPL", "MSFT"],
                    "cleanTask": "get price to earnings ratios for AAPL and MSFT",
                    "description": "Retrieve price to earnings ratios for Apple and Microsoft"
                },
                {
                    "id": "t2",
                    "parent": ["t1"],
                    "tag": "analyze",
                    "entity": ["AAPL", "MSFT"],
                    "cleanTask": "compare price to earnings ratios between AAPL and MSFT",
                    "description": "Analyze and compare the PE ratios of both companies"
                }
            ])
        ).with_inputs("query"),
        
        dspy.Example(
            query="What are the top 5 technology stocks by market cap?",
            expected_plan=json.dumps([
                {
                    "id": "t1",
                    "parent": None,
                    "tag": "collect_data",
                    "entity": None,
                    "cleanTask": "identify the top 5 technology stocks by market capitalization",
                    "description": "Identify the five largest technology companies by market cap"
                },
                {
                    "id": "t2",
                    "parent": ["t1"],
                    "tag": "collect_data",
                    "entity": ["identified_stocks"],
                    "cleanTask": "get market capitalization data for identified technology stocks",
                    "description": "Retrieve market cap data for the identified companies"
                },
                {
                    "id": "t3",
                    "parent": ["t2"],
                    "tag": "analyze",
                    "entity": ["identified_stocks"],
                    "cleanTask": "rank technology stocks by market capitalization",
                    "description": "Analyze and rank the stocks by market cap"
                }
            ])
        ).with_inputs("query"),
        
        dspy.Example(
            query="Show me Apple's stock price trend for the last 6 months",
            expected_plan=json.dumps([
                {
                    "id": "t1",
                    "parent": None,
                    "tag": "collect_data",
                    "entity": "AAPL",
                    "cleanTask": "get AAPL stock price data for the past 6 months",
                    "description": "Retrieve Apple's stock price data for the last 6 months"
                },
                {
                    "id": "t2",
                    "parent": ["t1"],
                    "tag": "visualize",
                    "entity": "AAPL",
                    "cleanTask": "create line chart of AAPL stock price trend over 6 months",
                    "description": "Generate a line chart showing Apple's stock price trend"
                }
            ])
        ).with_inputs("query"),
        
        dspy.Example(
            query="Get Tesla's financial data",
            expected_plan=json.dumps([
                {
                    "id": "t1",
                    "parent": None,
                    "tag": "collect_data",
                    "entity": "TSLA",
                    "cleanTask": "get financial metrics for TSLA",
                    "description": "Retrieve Tesla's financial data including revenue, earnings, and ratios"
                }
            ])
        ).with_inputs("query"),
        
        dspy.Example(
            query="Analyze Amazon's performance",
            expected_plan=json.dumps([
                {
                    "id": "t1",
                    "parent": None,
                    "tag": "collect_data",
                    "entity": "AMZN",
                    "cleanTask": "get AMZN financial metrics and stock performance data",
                    "description": "Retrieve Amazon's financial and performance data"
                },
                {
                    "id": "t2",
                    "parent": ["t1"],
                    "tag": "analyze",
                    "entity": "AMZN",
                    "cleanTask": "analyze AMZN financial performance and trends",
                    "description": "Analyze Amazon's financial health and performance trends"
                }
            ])
        ).with_inputs("query")
    ]
    
    return training_examples

def plan_quality_metric(example: dspy.Example, pred: dspy.Prediction, trace=None) -> float:
    """
    Evaluate the quality of a generated task plan.
    
    Checks for:
    1. Valid JSON format
    2. Proper task structure
    3. Correct dependency chains
    4. Appropriate task tags
    5. Entity standardization
    """
    
    try:
        # Parse the generated plan
        generated_tasks = json.loads(pred.plan_json)
        
        if not isinstance(generated_tasks, list):
            return 0.0
        
        score = 0.0
        max_score = 5.0
        
        # 1. Valid JSON structure (1 point)
        if all(isinstance(task, dict) for task in generated_tasks):
            score += 1.0
        
        # 2. Required fields present (1 point)
        required_fields = ['id', 'tag', 'cleanTask', 'description']
        if all(all(field in task for field in required_fields) for task in generated_tasks):
            score += 1.0
        
        # 3. Valid task tags (1 point)
        valid_tags = ['collect_data', 'analyze', 'visualize', 'summarize', 'general_qa']
        if all(task.get('tag') in valid_tags for task in generated_tasks):
            score += 1.0
        
        # 4. Proper dependency structure (1 point)
        task_ids = [task.get('id') for task in generated_tasks]
        valid_dependencies = True
        for task in generated_tasks:
            parent = task.get('parent')
            if parent:
                if isinstance(parent, list):
                    valid_dependencies &= all(p in task_ids for p in parent)
                else:
                    valid_dependencies &= parent in task_ids
        
        if valid_dependencies:
            score += 1.0
        
        # 5. Financial relevance (1 point)
        finance_keywords = ['stock', 'price', 'financial', 'market', 'ratio', 'earnings', 'revenue']
        query_lower = example.query.lower()
        if any(keyword in query_lower for keyword in finance_keywords):
            if len(generated_tasks) > 0:  # Finance queries should generate tasks
                score += 1.0
        else:
            score += 1.0  # Non-finance queries are valid
        
        return score / max_score
        
    except (json.JSONDecodeError, KeyError, TypeError):
        return 0.0

def plan_completeness_metric(example: dspy.Example, pred: dspy.Prediction, trace=None) -> bool:
    """Binary metric checking if plan addresses the query completely."""
    
    try:
        generated_tasks = json.loads(pred.plan_json)
        
        # Check if plan is not empty for finance-related queries
        finance_keywords = ['stock', 'price', 'financial', 'market', 'ratio', 'earnings', 'revenue']
        query_lower = example.query.lower()
        
        if any(keyword in query_lower for keyword in finance_keywords):
            return len(generated_tasks) > 0
        
        return True  # Non-finance queries can have empty plans
        
    except:
        return False

# ============================================================================
# STAGE 3: DSPy OPTIMIZATION - Training and Improvement
# ============================================================================

class OptimizedTaskPlanner:
    """Wrapper class for optimized DSPy task planner."""
    
    def __init__(self):
        self.base_planner = DSPyTaskPlanner()
        self.optimized_planner = None
        self.is_optimized = False
        
    def optimize(self, trainset: List[dspy.Example], devset: List[dspy.Example] = None):
        """Optimize the task planner using DSPy optimizers."""
        
        if devset is None:
            # Split training set for dev evaluation
            split_point = int(len(trainset) * 0.8)
            train_split = trainset[:split_point]
            devset = trainset[split_point:]
        else:
            train_split = trainset
        
        print("🚀 Starting DSPy optimization...")
        
        # Configure optimizer
        config = dict(
            max_bootstrapped_demos=4, 
            max_labeled_demos=2, 
            num_candidate_programs=8,
            num_threads=2
        )
        
        # Use BootstrapFewShotWithRandomSearch for optimization
        optimizer = BootstrapFewShotWithRandomSearch(
            metric=plan_quality_metric, 
            **config
        )
        
        # Compile (optimize) the program
        print(f"📊 Training on {len(train_split)} examples, evaluating on {len(devset)} examples")
        
        self.optimized_planner = optimizer.compile(
            self.base_planner, 
            trainset=train_split
        )
        
        self.is_optimized = True
        
        # Evaluate performance
        if devset:
            evaluator = Evaluate(devset=devset, display_progress=True, display_table=3)
            
            print("\n📈 Evaluating base planner...")
            base_score = evaluator(self.base_planner, metric=plan_quality_metric)
            
            print("\n🎯 Evaluating optimized planner...")
            optimized_score = evaluator(self.optimized_planner, metric=plan_quality_metric)
            
            print(f"\n📊 Results:")
            print(f"Base planner score: {base_score:.3f}")
            print(f"Optimized planner score: {optimized_score:.3f}")
            print(f"Improvement: {optimized_score - base_score:.3f}")
            
        print("✅ Optimization complete!")
    
    def plan_tasks(self, query: str) -> List[Dict[str, Any]]:
        """Generate task plan using the optimized planner."""
        
        planner = self.optimized_planner if self.is_optimized else self.base_planner
        
        try:
            result = planner(query=query)
            tasks_json = result.plan_json
            
            # Debug: Print the raw output
            print(f"🔍 Raw DSPy output: {tasks_json}")
            
            # Clean the JSON string (remove markdown code blocks if present)
            if tasks_json.startswith("```json"):
                tasks_json = tasks_json.replace("```json", "").replace("```", "").strip()
            elif tasks_json.startswith("```"):
                tasks_json = tasks_json.replace("```", "").strip()
            
            # Parse JSON
            tasks = json.loads(tasks_json)
            
            # Ensure tasks is a list
            if not isinstance(tasks, list):
                print(f"⚠️ Expected list, got {type(tasks)}")
                return []
            
            # Convert to Task objects for validation
            validated_tasks = []
            for i, task_data in enumerate(tasks):
                try:
                    # Ensure task_data is a dictionary
                    if not isinstance(task_data, dict):
                        print(f"⚠️ Task {i} is not a dict: {task_data}")
                        continue
                    
                    # Add missing required fields with defaults
                    if "id" not in task_data:
                        task_data["id"] = f"t{i+1}"
                    if "tag" not in task_data:
                        task_data["tag"] = "general_qa"
                    if "cleanTask" not in task_data:
                        task_data["cleanTask"] = "Process task"
                    if "description" not in task_data:
                        task_data["description"] = "Task description"
                    
                    # Validate with Pydantic
                    task = Task(**task_data)
                    validated_tasks.append(task.model_dump())
                    
                except Exception as e:
                    print(f"⚠️ Task validation error for task {i}: {e}")
                    print(f"🔍 Task data: {task_data}")
                    continue
            
            print(f"✅ Validated {len(validated_tasks)} tasks")
            return validated_tasks
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"🔍 Raw output: {tasks_json}")
            return self._get_fallback_plan(query)
            
        except Exception as e:
            print(f"⚠️ Planning error: {e}")
            return self._get_fallback_plan(query)
    
    def _get_fallback_plan(self, query: str) -> List[Dict[str, Any]]:
        """Generate a simple fallback plan when DSPy fails."""
        return [{
            "id": "t1",
            "parent": None,
            "tag": "general_qa",
            "entity": None,
            "cleanTask": f"address query: {query}",
            "description": "General response to user query"
        }]
    
    def save_optimized_model(self, path: str):
        """Save the optimized planner to disk."""
        if self.optimized_planner:
            self.optimized_planner.save(path)
            print(f"💾 Optimized planner saved to {path}")
        else:
            print("⚠️ No optimized planner to save. Run optimize() first.")
    
    def load_optimized_model(self, path: str):
        """Load an optimized planner from disk."""
        try:
            self.optimized_planner = DSPyTaskPlanner()
            self.optimized_planner.load(path)
            self.is_optimized = True
            print(f"📂 Optimized planner loaded from {path}")
        except Exception as e:
            print(f"⚠️ Failed to load optimized planner: {e}")

# ============================================================================
# LANGGRAPH INTEGRATION
# ============================================================================

# Initialize the optimized planner
global_planner = OptimizedTaskPlanner()

def plan_tasks_node(state: PlannerState):
    """LangGraph node that uses DSPy for task planning."""
    tasks = global_planner.plan_tasks(state['query'])
    return {"tasks": [Task(**task) for task in tasks]}

def initialize_planner_with_training():
    """Initialize and optionally train the planner."""
    
    # Create training dataset
    trainset = create_evaluation_dataset()
    
    # Check if we should train (you can make this configurable)
    should_train = os.getenv("TRAIN_PLANNER", "false").lower() == "true"
    
    if should_train and len(trainset) > 2:
        print("🎓 Training DSPy planner...")
        global_planner.optimize(trainset)
    else:
        print("🔧 Using base DSPy planner (no training)")

# Build LangGraph
builder = StateGraph(PlannerState)
builder.add_node("planner", plan_tasks_node)
builder.add_edge(START, "planner")
builder.add_edge("planner", END)
planner_chain = builder.compile()

def get_plan(user_query: str) -> List[dict]:
    """Main interface function for getting task plans."""
    result = planner_chain.invoke({"query": user_query})
    return [task.model_dump() for task in result["tasks"]]

# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    from pprint import pprint
    
    # Initialize the planner (with optional training)
    initialize_planner_with_training()
    
    # Load demo queries from JSON file
    try:
        with open('demo_queries.json', 'r', encoding='utf-8') as f:
            demo_queries = json.load(f)
    except FileNotFoundError:
        print("demo_queries.json not found. Using default queries.")
        demo_queries = [
            "Compare Apple and Microsoft stock performance",
            "What are the top 5 tech stocks?",
            "Show me Tesla's earnings trend",
            "Analyze the financial health of Amazon"
        ]
    
    results = []
    
    print(f"\n🎯 Processing {len(demo_queries)} demo queries...")
    
    for i, q in enumerate(demo_queries, 1):
        print(f"\n=== Demo {i}: {q}")
        try:
            result = get_plan(q)
            query_result = {
                "demo_number": i,
                "query": q,
                "tasks": result,
                "status": "success",
                "planner_type": "optimized" if global_planner.is_optimized else "base"
            }
            results.append(query_result)
            print(f"✅ Success - Generated {len(result)} tasks")
            
            # Show first task as example
            if result:
                print(f"📋 First task: {result[0]['cleanTask']}")
                
        except Exception as e:
            query_result = {
                "demo_number": i,
                "query": q,
                "tasks": [],
                "status": "error",
                "error": str(e)
            }
            results.append(query_result)
            print(f"❌ Error: {e}")
    
    # Save results
    output_file = "results/dspy_planner_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to {output_file}")
    print(f"📊 Processed {len(demo_queries)} queries")
    print(f"✅ Successful: {len([r for r in results if r['status'] == 'success'])}")
    print(f"❌ Failed: {len([r for r in results if r['status'] == 'error'])}")
    
    # Optional: Save the optimized model
    if global_planner.is_optimized:
        model_path = "results/optimized_planner.json"
        global_planner.save_optimized_model(model_path)