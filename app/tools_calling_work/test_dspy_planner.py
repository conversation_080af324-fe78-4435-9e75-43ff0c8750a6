#!/usr/bin/env python3
"""
Test script for DSPy planner - demonstrates usage and capabilities
"""

import json
from pprint import pprint
from planner_dspy import get_plan

def test_dspy_planner():
    """Test the DSPy planner with various query types."""
    
    print("🚀 TESTING DSPy PLANNER")
    print("=" * 50)
    
    # Test queries covering different scenarios
    test_queries = [
        # Financial analysis
        "Compare Apple and Microsoft stock performance over the last year",
        
        # Data collection and visualization
        "Get Tesla's stock price and create a chart showing the trend",
        
        # Simple data request
        "What is Amazon's current market cap?",
        
        # Complex multi-step analysis
        "Analyze the top 5 tech stocks, compare their P/E ratios, and recommend the best investment",
        
        # Non-actionable query
        "Hello, how are you today?",
        
        # Weather query (non-financial)
        "What's the weather like in New York today?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 TEST {i}: {query}")
        print("-" * 60)
        
        try:
            # Get plan from DSPy planner
            tasks = get_plan(query)
            
            if not tasks:
                print("✅ No tasks generated (appropriate for non-actionable query)")
            else:
                print(f"✅ Generated {len(tasks)} tasks:")
                for j, task in enumerate(tasks, 1):
                    print(f"\n  Task {j}:")
                    print(f"    ID: {task['id']}")
                    print(f"    Tag: {task['tag']}")
                    print(f"    Entity: {task.get('entity', 'None')}")
                    print(f"    Clean Task: {task['cleanTask']}")
                    print(f"    Description: {task['description'][:80]}...")
                    if task.get('parent'):
                        print(f"    Parent: {task['parent']}")
        
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n🎉 TESTING COMPLETE!")
    print("=" * 50)

def compare_with_sample_query():
    """Compare DSPy output with a known good example."""
    
    print("\n🔍 DETAILED COMPARISON WITH SAMPLE QUERY")
    print("=" * 50)
    
    sample_query = "Pull Netflix's annual revenue for the last ten years, compute the compound annual growth rate, and summarize the trend."
    
    print(f"Query: {sample_query}")
    print("\nDSPy Planner Output:")
    
    tasks = get_plan(sample_query)
    
    for task in tasks:
        print(f"\nTask {task['id']}:")
        print(f"  Tag: {task['tag']}")
        print(f"  Entity: {task.get('entity', 'None')}")
        print(f"  Clean Task: {task['cleanTask']}")
        print(f"  Description: {task['description']}")
        if task.get('parent'):
            print(f"  Parent: {task['parent']}")
    
    print(f"\n✅ Generated {len(tasks)} tasks with proper dependencies")

def demonstrate_optimization():
    """Show how optimization works."""
    
    print("\n🎯 OPTIMIZATION DEMONSTRATION")
    print("=" * 50)
    
    print("The DSPy planner includes automatic optimization capabilities:")
    print("1. Uses BootstrapFewShot optimizer")
    print("2. Learns from training examples")
    print("3. Improves task decomposition quality")
    print("4. Validates and refines outputs")
    
    print("\nTo run optimization:")
    print("python app/tools_calling_work/planner_dspy.py")
    print("(Choose 'y' when prompted)")
    
    print("\nOptimization benefits:")
    print("- Better task breakdown")
    print("- More accurate entity identification")
    print("- Improved dependency management")
    print("- Reduced validation errors")

if __name__ == "__main__":
    # Run all tests
    test_dspy_planner()
    compare_with_sample_query()
    demonstrate_optimization()
    
    print(f"\n📚 For more information, see:")
    print("- planner_dspy.py - Main implementation")
    print("- DSPy_Planner_Summary.md - Detailed documentation")
    print("- compare_planners.py - Performance comparison")
