import datetime
import logging

from langchain_openai import ChatOpenAI
import agentops


from typing import List, Optional, Literal, TypedDict
from langgraph.types import Command
from app.agent.prompts import *
from app.core.config import settings
from langgraph.prebuilt import create_react_agent
# from app.state.utilities import State, make_supervisor_node
from app.tools.web_search.agent_tools import web_search_deep
from app.tools.internal_search.helper import qualitative_analysis_tool, quantitative_analysis_tool
from langgraph.graph import StateGraph, MessagesState, START, END
from langchain_core.messages import HumanMessage,SystemMessage
from pydantic import BaseModel, Field
from langgraph.checkpoint.memory import MemorySaver
#from langgraph.checkpoint.sqlite import SqliteSaver
#import sqlite3
from langchain_community.callbacks import get_openai_callback
from app.tools.workflows.helper import analyst_sentiment_analysis, comprehensive_company_analysis, crypto_market_sentiment_price_analysis, dividend_strategy_income_analysis, earnings_and_transcript_analysis, esg_sustainability_analysis, etf_mutual_fund_holding_analysis, forex_and_commodity_trading_analysis, insider_Trading_institutional_ownership_analysis, ipo_market_trends_analysis, macro_economic_sector_analysis, market_data_technical_analysis, market_movers_analysis, market_news_analysis, merger_and_acquisition_analysis, technical_analysis, valuation_dcf_analysis

# logging.basicConfig(level=logging.WARNING)


# agentops.init("9dd94629-52f9-4750-856e-72018017a28b", default_tags=["autogen", "swarm"])

# For trace logging.
# trace_logger = logging.getLogger(TRACE_LOGGER_NAME)
# trace_logger.addHandler(logging.StreamHandler())
# trace_logger.setLevel(logging.DEBUG)


# Track Agent Actions
def track_agent_action(agent_name, action_name, status, additional_metadata=None):
    metadata = {
        "status": status,
        "action_name": action_name,
        "agent_name": agent_name,
        "symbol": additional_metadata['symbol'],  # Add symbol for better identification
        "time_started": datetime.datetime.now().isoformat(),  # Add timestamp
        **(additional_metadata or {})
    }
    agentops.track_agent(
        name=f"{agent_name} - {action_name}",
        tags=["agent", agent_name, status],
        metadata=metadata
    )

# Track Tool Usage
def track_tool_usage(tool_name, status, metadata=None):
    agentops.track_tool(
        name=f"{tool_name} Execution",
        tags=["tool", tool_name, status],
        metadata={**{"status": status}, **(metadata or {})}
    )

# Track Handoffs Between Agents
def track_handoff(from_agent, to_agent, status, metadata=None):
    agentops.track_agent(
        name=f"{from_agent} to {to_agent} Handoff",
        tags=["handoff", from_agent, to_agent, status],
        metadata={**{"status": status}, **(metadata or {})}
    )


# Use in-memory saver for LangGraph checkpoints
# LangMem will handle the vector storage separately
memory_saver = MemorySaver()
logging.info("Using in-memory saver for LangGraph checkpoints")

async def create_agent(symbol,report, deep_search=False):
    llm = ChatOpenAI(model="gpt-4.1-nano-2025-04-14")

    class Route(BaseModel):
        step: List[Literal["web_researcher_node", "internal_search_node", "workflow_node", "normal_chat_node"]] = Field(
            default_factory=list,
            description="List of next steps in the routing process; can include one or more steps."
        )


# Augment the LLM with schema for structured output
    router = llm.with_structured_output(Route)

    class State(TypedDict):
        topic: str
        combined_output: str
        input: str
        decision: List[str]
        output_from_web: str
        output_from_internal: str
        output_from_workflow: str
        output_from_normal_chat: str
        output: str

    writer_agent = create_react_agent(llm,tools=[] ,prompt=writer_prompt(report))

    workflow_agent = create_react_agent(llm,
                    tools=[
                comprehensive_company_analysis,
                market_news_analysis,
                market_movers_analysis,
                analyst_sentiment_analysis,
                technical_analysis,
                earnings_and_transcript_analysis,
                valuation_dcf_analysis,
                esg_sustainability_analysis,
                macro_economic_sector_analysis,
                etf_mutual_fund_holding_analysis,
                market_data_technical_analysis,
                insider_Trading_institutional_ownership_analysis,
                dividend_strategy_income_analysis,
                merger_and_acquisition_analysis,
                ipo_market_trends_analysis,
                forex_and_commodity_trading_analysis,
                crypto_market_sentiment_price_analysis,

                    ],
                    prompt=workflow_prompt())
    internal_search_agent = create_react_agent(llm, tools=[quantitative_analysis_tool, qualitative_analysis_tool],prompt=internal_search_prompt())
    web_research_agent = create_react_agent(llm, tools=[web_search_deep],prompt=web_search_prompt())

    def filter_messages(messages):
        """Keep only the last 5 messages to prevent context window overflow"""
        if isinstance(messages, list) and len(messages) > 5:
            return messages[-5:]
        return messages

    # Nodes
    async def web_researcher_node(state: State):
        """First LLM call to generate detailed research report on upto date data """
        filtered_input = filter_messages(state["input"]) if isinstance(state["input"], list) else state["input"]
        msg = await web_research_agent.ainvoke({"messages": filtered_input})
        tool_messages = [m for m in msg["messages"] if m.type == "tool"]
        return {"output_from_web": tool_messages[0].content}


    async def internal_search_node(state: State):
        """Second LLM call to generate internal_search about qualitative and quantitative analysis"""
        filtered_input = filter_messages(state["input"]) if isinstance(state["input"], list) else state["input"]
        msg = await internal_search_agent.ainvoke({"messages": filtered_input})
        return {"output_from_internal": msg["messages"][-1].content}


    async def workflow_node(state: State):
        """You are the 'workflows' agent responsible for conducting detailed financial and market research using specialized tools like comprehensive_company_analysis, market_news_analysis, and others."""
        filtered_input = filter_messages(state["input"]) if isinstance(state["input"], list) else state["input"]
        msg = await workflow_agent.ainvoke({"messages": filtered_input})
        return {"output_from_workflow": msg["messages"][-1].content}

    async def normal_chat_node(state: State):
        """You are the 'normal_chat' agent responsible for replying to the user's question."""
        filtered_input = filter_messages(state["input"]) if isinstance(state["input"], list) else state["input"]
        # msg =  llm.invoke({"messages": filtered_input})
        return {"output_from_normal_chat": filtered_input}

    async def writer_node(state: State):
        """You are the 'writer' agent responsible for returing the final report you will use the combined_output from the state and will use the writer_prompt to write the final report but you can answer the question directly if you have the information rather than using the prompt"""
        # Combined output might be a string, so only filter if it's a list
        filtered_output = filter_messages(state["combined_output"]) if isinstance(state["combined_output"], list) else state["combined_output"]

        # Invoke the writer agent
        msg = await writer_agent.ainvoke({"messages": filtered_output})

        return {"output": msg["messages"][-1].content}

    def supervisor_node(state: State):
        """Route the input to the appropriate node"""
        filtered_input = filter_messages(state["input"]) if isinstance(state["input"], list) else state["input"]
        # Run the augmented LLM with structured output to serve as routing logic
        decision = router.invoke(
            [
                SystemMessage(
                    content=f"""You are a task routing coordinator responsible for directing research queries to specialized nodes.
                     {f'- If no company name or ticker is provided in the query, use this stock ticker symbol {symbol} by default and use this as ticker.' if symbol != 'TDGPT' else ''}

                    Available nodes and their specialties:
                    1. web_research_node:
                       - Real-time market data and news
                       - Current events and trends
                       - Latest company updates and announcements

                    2. internal_search_node:
                       - Quantitative analysis (financial metrics, ratios)
                       - Qualitative analysis (business model, competitive advantages)
                       - Historical performance data

                    3. workflow_node:
                       - Financial: Company analysis, valuations, earnings
                       - Market: Technical analysis, sentiment, movers
                       - Investment: ETFs, dividends, insider trading
                       - Macro: Sector analysis, M&A, IPOs
                       - Alternative: Forex, crypto, commodities

                    4. normal_chat_node:
                       - For queries that don't require any market research, financial analysis, or data retrieval
                       - General conversation and basic interactions
                       - Questions about the system itself or how to use it
                       - Personal introductions or greetings
                       Examples:
                       - "How are you doing today?"
                       - "What's your name?"
                       - "Can you help me understand how to use this system?"
                       - "Tell me about yourself"
                       - "What can you do?"
                       - "Thanks for your help"
                       - Any other queries not related to financial markets or research

                    Instructions:
                      - Analyze the input query and determine which node(s) would provide the most relevant information
                      - Prioritize internal_search_node for historical/fundamental data
                      - Use web_research_node for time-sensitive information
                      - Choose workflow_node for comprehensive analysis requests
                      - Choose normal_chat_node for general questions which doesnt require research.
                      - You can route to multiple nodes if the query requires different types of analysis

                    Return the most appropriate node(s) based on the query's requirements."""
                ),
                HumanMessage(content=filtered_input),
            ]
        )

        return {"decision": decision.step}

    def supervisor_decision(state: State):
        # decision_main = []
        # for step in state["decision"]:
        #     if step == "web_research":
        #         decision_main.append("web_researcher_node")
        #     elif step == "internal_search":
        #         decision_main.append("internal_search_node")
        #     elif step == "workflow":
        #         decision_main.append("workflow_node")
        # return decision_main
    # Return the node name you want to visit next
        # if state["decision"] == "web_research":
        #     return "web_researcher_node"
        # elif state["decision"] == "internal_search":
        #     return "internal_search_node"
        # elif state["decision"] == "workflow":
        #     return "workflow_node"
        return state["decision"]

    def aggregator(state: State):
        """Combine the joke and story into a single output"""

        combined = f"Here's a complete answers from all the nodes for {state['input']}!\n\n"

        if 'output_from_web' in state and state['output_from_web']:
            combined += f"(Web Research):\n{state['output_from_web']}\n\n"

        if 'output_from_internal' in state and state['output_from_internal']:
            combined += f"(Internal Search):\n{state['output_from_internal']}\n\n"

        if 'output_from_workflow' in state and state['output_from_workflow']:
            combined += f"(Workflow):\n{state['output_from_workflow']}\n\n"

        if 'output_from_normal_chat' in state and state['output_from_normal_chat']:
            combined += f"(Normal Chat):\n{state['output_from_normal_chat']}\n\n"
        return {"combined_output": combined}


    # Build workflow
    parallel_builder = StateGraph(State)

    # Add nodes
    parallel_builder.add_node("web_researcher_node", web_researcher_node)
    parallel_builder.add_node("internal_search_node", internal_search_node)
    parallel_builder.add_node("workflow_node", workflow_node)
    parallel_builder.add_node("normal_chat_node", normal_chat_node)
    parallel_builder.add_node("supervisor_node", supervisor_node)
    parallel_builder.add_node("aggregator", aggregator)
    parallel_builder.add_node("writer_node", writer_node)

    # Add edges to connect nodes
    intermediate_nodes = ["web_researcher_node", "internal_search_node", "workflow_node", "normal_chat_node"]
    parallel_builder.add_edge(START, "supervisor_node")
    parallel_builder.add_conditional_edges(
        "supervisor_node",
        supervisor_decision,
        intermediate_nodes,
    )
    parallel_builder.add_edge("web_researcher_node", "aggregator")
    parallel_builder.add_edge("internal_search_node", "aggregator")
    parallel_builder.add_edge("normal_chat_node", "aggregator")
    parallel_builder.add_edge("workflow_node", "aggregator")
    parallel_builder.add_edge("aggregator", "writer_node")
    parallel_builder.add_edge("writer_node", END)
    return parallel_builder.compile(checkpointer=memory_saver)

