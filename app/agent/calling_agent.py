import json
import asyncio
import logging
import datetime
from app.agent.agent_creation import create_agent
from langchain_community.callbacks import get_openai_callback
from app.core.senitel_filter import <PERSON><PERSON>ilter
from app.memory.memory_manager import search_memories, format_memory_context
from app.logging_custom_directory.logger_custom import logger

# Import mem0 helper for integrated usage
from app.memory.mem0_helper import (
    search_mem0_memories,
    is_mem0_available,
    format_mem0_context
)

# Import background memory storage
from app.services.background_tasks import queue_memory_storage

# history = []

def filter_history(history, max_length=6):
       """Keep only the most recent history items"""
       return history[-max_length:] if len(history) > max_length else history



async def generating_reply(
    task, symbol, user_id, report, deep_search, history, chat_id=None, longterm=True, verbose=False
) -> None:
    research_team = await create_agent(symbol, report, deep_search=deep_search)
    # Use chat_id in the thread_id if provided, otherwise use user_id
    thread_id = chat_id if chat_id else user_id
    logger.info(f"Using thread_id: {thread_id}")
    config = {"configurable": {"thread_id": thread_id}}

    # Search for relevant memories using both systems
    memory_context = ""
    has_memories = False

    # 1. Search using mem0 if available
    mem0_memories = []
    if longterm and is_mem0_available():
        try:
            mem0_memories = await search_mem0_memories(
                user_id=user_id,
                query=task,
                chat_id=thread_id,
                limit=3
            )
            if mem0_memories:
                has_memories = True
                logger.info(f"Found {len(mem0_memories)} memories using mem0 for user {user_id}")
        except Exception as e:
            logger.error(f"Error searching with mem0: {e}")

    # 2. Search using existing memory system
    existing_memory_results = None
    try:
        existing_memory_results = await search_memories(
            user_id=user_id,
            query=task,
            limit=3,
            include_conversation_history=True,
            chat_id=thread_id
        )
        if existing_memory_results and existing_memory_results.get("results"):
            has_memories = True
            logger.info(f"Found {len(existing_memory_results.get('results', []))} memories using existing system for user {user_id}")
    except Exception as e:
        logger.error(f"Error searching with existing memory system: {e}")

    # # Format combined memory context with single header
    if has_memories:
        #memory_context = "\n\nRelevant information from previous conversations:\n"

        # Add mem0 memories first (without header)
        if mem0_memories:
            mem0_context = format_mem0_context(mem0_memories, include_header=False)
            if mem0_context:
                #memory_context += mem0_context + "\n"
                memory_context += f"\n💡User's Recent Insights & Preferences:\n{mem0_context}"

        # Add existing memory results (without header)
        if existing_memory_results and existing_memory_results.get("results"):
            existing_context = format_memory_context(existing_memory_results, include_answers=True, include_header=True)
            if existing_context:
                memory_context += existing_context

    # try:
    #     # Use chat_id in the state file name if provided
    #     state_file = f"assistant_state_{thread_id}.json"
    #     with open(state_file, "r") as f:
    #         state = json.load(f)
    #     #await research_team.load_state(state)
    # except:
    #     logger.info("No previous state found, starting fresh")

    # Filter history
    filtered_history = filter_history(history)

    #  # Create prompt with memory context
    # from langchain_core.messages import SystemMessage, HumanMessage
    # msgs = []
    # if memory_context:
    #     msgs.append(
    #         SystemMessage(content=memory_context),
    #     )
    # # always append the current user query as its own turn
    # msgs.append(HumanMessage(content=f"User query: {task}"))

    # # now feed the StateGraph the proper list
    # state = {"input": msgs}

    # Create prompt with memory context
    if memory_context:
        #prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n{memory_context}"
        prompt = f"user message: {task} \n\n{memory_context}"
    else:
        prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n"

    state = {
        "input": f"{prompt}"
    }

    # Invoke the agent
    with get_openai_callback() as cb:
        result = await research_team.ainvoke(state, config=config)
        prompt_token = cb.prompt_tokens
        completion_token = cb.completion_tokens
        logger.info(f"Tokens used - Prompt: {prompt_token}, Completion: {completion_token}")

    # Queue conversation storage for background processing (OPTIMIZED)
    try:
        # Format messages for storage
        messages = [
            {"role": "user", "content": task},
            {"role": "assistant", "content": result["output"]}
        ]

        # Queue for background storage - this returns immediately
        await queue_memory_storage(
            user_id=user_id,
            chat_id=thread_id,
            messages=messages,
            symbol=symbol,
            task_type="both" if longterm else "existing"  # Store in both if longterm=True, only existing if longterm=False
        )
        
        logger.info(f"Queued conversation for background storage - user {user_id}, chat {thread_id}")

    except Exception as e:
        logger.error(f"Error queuing conversation for background storage: {e}")

    return result, prompt_token, completion_token


class ReplyStream:
    def __init__(self):
        self.full_message_list = {"messages": []}
        self.completion_token = 0
        self.prompt_token = 0
        self.final_response = ""


    async def generate(self, task, symbol, user_id, report, deep_search, history, chat_id=None, longterm=True, verbose=False):
        research_team = await create_agent(symbol, report, deep_search=deep_search)
        # Use chat_id in the thread_id if provided, otherwise use user_id
        thread_id = chat_id if chat_id else user_id
        logger.info(f"Streaming with thread_id: {thread_id}")
        config = {"configurable": {"thread_id": thread_id}}

        # Search for relevant memories using both systems
        memory_context = ""
        has_memories = False

        # 1. Search using mem0 if available
        mem0_memories = []
        if longterm and is_mem0_available():
            try:
                mem0_memories = await search_mem0_memories(
                    user_id=user_id,
                    query=task,
                    chat_id=thread_id,
                    limit=3
                )
                if mem0_memories:
                    has_memories = True
                    logger.info(f"Found {len(mem0_memories)} memories using mem0 for user {user_id}")
            except Exception as e:
                logger.error(f"Error searching with mem0: {e}")

        # 2. Search using existing memory system
        existing_memory_results = None
        try:
            existing_memory_results = await search_memories(
                user_id=user_id,
                query=task,
                limit=3,
                include_conversation_history=True,
                chat_id=thread_id
            )
            if existing_memory_results and existing_memory_results.get("results"):
                has_memories = True
                logger.info(f"Found {len(existing_memory_results.get('results', []))} memories using existing system for user {user_id}")
        except Exception as e:
            logger.error(f"Error searching with existing memory system: {e}")

        # # Format combined memory context with single header
        if has_memories:
            #memory_context = "\n\nRelevant information from previous conversations:\n"

            # Add mem0 memories first (without header)
            if mem0_memories:
                mem0_context = format_mem0_context(mem0_memories, include_header=False)
                if mem0_context:
                    #memory_context += mem0_context + "\n"
                    memory_context += f"\n💡User's Recent Insights & Preferences:\n{mem0_context}"

            # Add existing memory results (without header)
            if existing_memory_results and existing_memory_results.get("results"):
                existing_context = format_memory_context(existing_memory_results, include_answers=True, include_header=True)
                if existing_context:
                    memory_context += existing_context

        # # Create prompt with memory context
        # from langchain_core.messages import SystemMessage, HumanMessage
        # msgs = []
        # if memory_context:
        #     msgs.append(
        #         SystemMessage(content=memory_context),
        #     )
        # # always append the current user query as its own turn
        # msgs.append(HumanMessage(content=f"User query: {task}"))

        # # now feed the StateGraph the proper list
        # state = {"input": msgs}

        # Create prompt with memory context
        if memory_context:
            #prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n{memory_context}"
            prompt = f"user message: {task} \n\n{memory_context}"
    
        else:
            prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n"

        state = {
            "input": f"{prompt}",
        }

        try:
            async for msg, metadata in research_team.astream(input=state, stream_mode="messages", config=config):
                if "writer_node" in metadata["langgraph_checkpoint_ns"]:
                    # Accumulate the response for storing in memory later
                    self.final_response += msg.content
                    yield(msg.content)

            self.prompt_token = 0
            self.completion_token = 0

            # Queue conversation storage for background processing (OPTIMIZED)
            if self.final_response and user_id and thread_id:
                try:
                    # Format messages for storage
                    messages = [
                        {"role": "user", "content": task},
                        {"role": "assistant", "content": self.final_response}
                    ]

                    # Queue for background storage - this returns immediately
                    await queue_memory_storage(
                        user_id=user_id,
                        chat_id=thread_id,
                        messages=messages,
                        symbol=symbol,
                        task_type="both" if longterm else "existing"  # Store in both if longterm=True, only existing if longterm=False
                    )
                    
                    logger.info(f"Queued streamed conversation for background storage - user {user_id}, chat {thread_id}")

                except Exception as e:
                    logger.error(f"Error queuing streamed conversation for background storage: {e}")

        except Exception as e:
            error_msg = f"[ERROR]: {str(e)}"
            logger.error(error_msg)
            yield error_msg


