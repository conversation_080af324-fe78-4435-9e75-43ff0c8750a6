import json
import os
from pathlib import Path
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import field_validator


class Settings(BaseSettings):
    OPENAI_APIKEY: str
    OPENAI_API_KEY: str
    # FIRECRAWL_API_KEY: str
    SCRAPER: str
    TAVILY_API_KEY: str
    FMP_BASE_URL: str
    FMP_API_KEY: str
    DECODER_SECRET_KEY: str
    DATABASE_URL: str
    ALPHAVANTAGE_API_KEY: str
    GCP_FILE_CRED: dict
    #GCP_FILE_CRED_PATH: str = "gcp_credentials.json"  # Path to the GCP credentials file
    AGENTOPS_API_KEY: str

    class Config:
        env_file = ".env"
        extra = "allow"

    # model_config = SettingsConfigDict(env_file=".env", extra="allow")

    # @property
    # def GCP_FILE_CRED(self) -> dict:
    #     """Read GCP credentials from the file specified in GCP_FILE_CRED_PATH."""
    #     try:
    #         file_path = Path(self.GCP_FILE_CRED_PATH)
    #         if not file_path.exists():
    #             print(f"Warning: GCP credentials file not found at {file_path}")
    #             return {}
            
    #         with open(file_path, 'r') as f:
    #             return json.load(f)
    #     except Exception as e:
    #         print(f"Error reading GCP credentials file: {e}")
    #         return {}

#     @field_validator('GCP_FILE_CRED', mode='before')
#     def parse_gcp_credentials(cls, v):
#         if isinstance(v, str):
#             try:
#                 # Replace literal \n with actual newlines to fix JSON parsing
#                 normalized_cred = v.replace('\\n', '\n')
#                 return json.loads(normalized_cred)
#             except json.JSONDecodeError as e:
#                 print(f"Error parsing GCP_FILE_CRED: {e}")
#                 return {}
#         return v

# # Set GCP credentials directly from file
# try:
#     with open("gcp_credentials.json", "r") as f:
#         os.environ["GCP_FILE_CRED"] = json.dumps(json.load(f))
# except Exception as e:
#     print(f"Error loading GCP credentials: {e}")

settings = Settings()
#credentials_dict = settings.GCP_FILE_CRED

