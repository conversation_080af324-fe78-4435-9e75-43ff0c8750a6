import psycopg2
from psycopg2 import sql
import json

from app.db import parameters
from app.logging_custom_directory.logger_custom import logger
from app.core.config import settings

class SQLTool():
    def __init__(self):
        # self.db_conventional_string = "***********************************************************************************/postgres"
        self.db_conventional_string = settings.DATABASE_URL

    def connect(self, database_name = "officefield_local"):
        try:
            if database_name == 'officefield_local':
                conn = self.make_connection(self.db_conventional_string)
            return conn
        except Exception as e:
            # logger_object['error'].log(e)
            logger.error(f"Error: {e} ")


    def make_connection(self, connection_string):
        try:
            return psycopg2.connect(connection_string)
        except Exception as e:
            logger.error(f"Error: {e} ")

    def generate_chat_id(self, user_id, symbol=None):
        """
        Generate a new chat_id for a user and symbol
        """
        import uuid
        import datetime

        # Generate a unique chat_id using timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

        # Include symbol in chat_id if provided
        if symbol:
            chat_id = f"{user_id}_{symbol}_{timestamp}"
        else:
            chat_id = f"{user_id}_{timestamp}"

        return chat_id

    def get_user_chats(self, user_id):
        """
        Get all chat_ids for a specific user
        """
        try:
            conn = self.connect(database_name = "officefield_local")
            q = """SELECT chat_id, MAX(created) as last_updated
                   FROM conversations_messages_testing
                   WHERE user_id = %s AND chat_id IS NOT NULL
                   GROUP BY chat_id
                   ORDER BY last_updated DESC"""

            cursor = conn.cursor()
            cursor.execute(q, (user_id,))
            rows = cursor.fetchall()
            conn.close()

            # Extract chat_ids from result
            chat_ids = [row[0] for row in rows]
            return chat_ids

        except Exception as e:
            logger.error(f"get_user_chats : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            return []

    def get_active_chat_id(self, user_id, symbol):
        """
        Get the most recent chat_id for a user and symbol
        Returns None if no chat exists
        """
        try:
            conn = self.connect(database_name = "officefield_local")
            q = """SELECT chat_id
                   FROM conversations_messages_testing
                   WHERE user_id = %s AND symbol = %s AND chat_id IS NOT NULL
                   ORDER BY created DESC
                   LIMIT 1"""

            cursor = conn.cursor()
            cursor.execute(q, (user_id, symbol))
            row = cursor.fetchone()
            conn.close()

            # Return the chat_id if found, otherwise None
            return row[0] if row else None

        except Exception as e:
            logger.error(f"get_active_chat_id : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            return None

    def get_chat_details(self, chat_id):
        """
        Get details about a specific chat
        """
        try:
            conn = self.connect(database_name = "officefield_local")
            q = """SELECT id, user_id, symbol, inputs, outputs, created
                   FROM conversations_messages_testing
                   WHERE chat_id = %s
                   ORDER BY created ASC"""

            cursor = conn.cursor()
            cursor.execute(q, (chat_id,))
            rows = cursor.fetchall()
            conn.close()

            # Format the results
            chat_details = []
            for row in rows:
                chat_details.append({
                    "id": row[0],
                    "user_id": row[1],
                    "symbol": row[2],
                    "input": row[3],
                    "output": row[4],
                    "created": row[5].isoformat() if row[5] else None
                })

            return chat_details

        except Exception as e:
            logger.error(f"get_chat_details : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            return []

    def write_response(self, data):
            try:
                conn = self.connect(database_name = "officefield_local")
                insert_query = parameters.SQL_WRITE_RPC
                insert_into = {
                                "user_id": int(data['user_id']),
                                "symbol": str(data['symbol']),
                                "tone": None,
                                "placable_vector_extrated_data": None,
                                "intial_conversation": None,
                                "templates": str(data['template']),  # correctly jsonb
                                "inputs": str(data['input']),
                                "outputs": str(data['output']),
                                "input_token": None,
                                "output_token": None,
                                "model_name": None,
                                "feedback_parity": None,
                                "feedback_explanation": None,
                                "data": psycopg2.extras.Json(data['displayable']),
                                "mapped_features": psycopg2.extras.Json(data['mapped_features']),
                                "created": data['created'],  # datetime
                                "status_var": 'Delivered',  # cast explicitly
                                "chat_id": str(data['chat_id'])
                            }
                cursor = conn.cursor()
                cursor.execute(insert_query, insert_into)
                rows = cursor.fetchall()[0][0]
                conn.commit()
                conn.close()
            except Exception as e:
                logger.error(f"Error: {e} ")
                conn.close()
                rows = None
            return rows

    def read_conversations_from_conventional_db(self, ask, user_id, symbol, chat_id=None):
        """
        Read conversation history from the database
        If chat_id is provided, filter by chat_id as well
        """
        try:
            conn = self.connect(database_name = "officefield_local")

            # Use the appropriate SQL query based on whether chat_id is provided
            # if chat_id:
            #     q = parameters.SQL_READ_RPC_WITH_CHAT
            #     params = {'ask': ask, 'user_id': user_id, "symbol": symbol, "chat_id": chat_id}
            # else:
            q = parameters.SQL_READ_RPC
            params = {'ask': ask, 'user_id': user_id, "symbol": symbol, "chat_id": chat_id}

            insert_query = sql.SQL(q)
            cursor = conn.cursor()
            cursor.execute(insert_query, params)
            rows = cursor.fetchall()
            rows = rows[0][0]
            conn.close()
        except Exception as e:
            logger.error(f"read_conversations_from_conventional_db : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            rows = None
        return rows['conversation']

    def read_data(self, user_id, symbol, user_ask, chat_id=None):
        """
        Read data from the database
        If chat_id is provided, filter by chat_id as well
        """
        try:
            conn = self.connect(database_name = "officefield_local")

            # Use the appropriate SQL query based on whether chat_id is provided
            # if chat_id:
            #     q = parameters.SQL_CONTEXT_RPC_WITH_CHAT
            #     params = {'user_id': user_id, "symbol": symbol, "ask": user_ask, "chat_id": chat_id}
            # else:
            q = parameters.SQL_CONTEXT_RPC
            params = {'user_id': user_id, "symbol": symbol, "ask": user_ask, "chat_id": chat_id}

            insert_query = sql.SQL(q)
            cursor = conn.cursor()
            cursor.execute(insert_query, params)
            rows = cursor.fetchall()
            conn.close()
        except Exception as e:
            logger.error(f"read_data : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            rows = None
        return rows

    def read_data_testing(self, user_id, symbol, chat_id=None):
        """
        Read test data from the database
        If chat_id is provided, filter by chat_id as well
        """
        try:
            conn = self.connect(database_name = "officefield_local")

            # Use the appropriate SQL query based on whether chat_id is provided
            # if chat_id:
            #     q = parameters.SQL_CONTEXT_RPC_TESTING_WITH_CHAT
            #     params = {'user_id': user_id, "symbol": symbol, "chat_id": chat_id}
            # else:
            q = parameters.SQL_CONTEXT_RPC_TESTING
            params = {'user_id': user_id, "symbol": symbol, "chat_id": chat_id}

            insert_query = sql.SQL(q)
            cursor = conn.cursor()
            cursor.execute(insert_query, params)
            rows = cursor.fetchall()
            rows = rows[0]
            conn.close()
        except Exception as e:
            logger.error(f"read_data_testing : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            rows = None
        return rows

    def update_read_data_testing(self, user_id, symbol,  limit=5, chat_id=None):

        conn = self.connect(database_name = "officefield_local")
        cursor = conn.cursor()

        try:
            # If chat_id is provided, use the updated stored procedure that includes chat_id
            # if chat_id:
            #     cursor.callproc('public.updated_read_context_v2_testing_with_chat', (symbol, user_id, limit, chat_id))
            # else:
            cursor.callproc('public.updated_read_context_v3', (symbol, user_id, limit, chat_id))
            conn.commit()
            rows = cursor.fetchone()
        except Exception as e:
            conn.rollback()
            print(f"An error occurred: {e}")
            logger.error(f"update_read_data_testing : Error: {e} ")

        finally:
            cursor.close()
            conn.close()
        return rows

    def fetche_template_context(self, user_id, symbol, chat_id=None):
        """
        Fetch template context from the database
        If chat_id is provided, filter by chat_id as well
        """
        try:
            conn = self.connect(database_name = "officefield_local")

            # Use the appropriate SQL query based on whether chat_id is provided
            # if chat_id:
            #     q = parameters.SQL_FETCHED_TEMPLATE_WITH_CHAT
            #     params = {'user_id': user_id, "symbol": symbol, "chat_id": chat_id}
            # else:
            q = parameters.SQL_FETCHED_TEMPLATE
            params = {'user_id': user_id, "symbol": symbol, "chat_id": chat_id}

            insert_query = sql.SQL(q)
            cursor = conn.cursor()
            cursor.execute(insert_query, params)
            rows = cursor.fetchall()
            conn.close()
        except Exception as e:
            logger.error(f"fetche_template_context : Error: {e} ")
            if 'conn' in locals() and conn:
                conn.close()
            rows = None
        return rows