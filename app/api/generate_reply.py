import datetime
import json
from typing import Annotated

from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from fastapi.security import OAuth2PasswordBearer

from app.core.config import settings
from app.core.Orchistration import Orchistration
from app.core.token_decoder import TokenDecoder
from app.db import db
from app.logging_custom_directory.logger_custom import logger

sqltool = db.SQLTool()
token_decode = TokenDecoder()
orch = Orchistration()

from app.agent import calling_agent
from app.api.api_payload_schema import UserAskRequest, DeleteMemoryRequest, GetMemoriesRequest

# Import memory management functions
from app.memory.mem0_helper import (
    get_all_mem0_memories,
    delete_mem0_memory,
    delete_all_mem0_memories,
    is_mem0_available
)

router = APIRouter(prefix="/api", tags=["Generate Reply"])
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


@router.post("/user_ask")
async def get_users(
    request: UserAskRequest, token: Annotated[str, Depends(oauth2_scheme)]
):
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    print("user_id: ", user_id)
    if user_id is not None:
        # Get chat_id from request, or find active chat, or generate a new one
        chat_id = request.chat_id

        # If no chat_id provided, try to find an active one for this user and symbol
        if not chat_id:
            # Check if user already has an active chat for this symbol
            active_chat_id = sqltool.get_active_chat_id(user_id, request.symbol)

            if active_chat_id:
                # Use the existing active chat
                chat_id = active_chat_id
                print("active chat_id: ", chat_id)
                logger.info(f"Using existing chat_id: {chat_id}")
            else:
                # No active chat found, generate a new one with symbol
                chat_id = sqltool.generate_chat_id(user_id, request.symbol)
                logger.info(f"Generated new chat_id: {chat_id}")

        history_coversation = orch.history_conversation(
            symbol=request.symbol, user_id=user_id, SqlTool=sqltool, chat_id=chat_id
        )

        final_output, prompt_token, completion_token= await calling_agent.generating_reply(
            request.task,
            request.symbol,
            user_id,
            request.report,
            request.deep_search,
            history=history_coversation,
            chat_id=chat_id,
            longterm=request.longterm,
            verbose=True,
        )
    else:
        return {"error": "token expired"}

    show_displayable = orch.Displayable_data_v2(conversation=final_output)
    # token_usage = orch.token_count_v2(conversation=conversation)



    # Write data to db
    data = {
        "input": request.task,
        "symbol": request.symbol,
        "user_id": user_id,
        "chat_id": chat_id,  # Include chat_id in the data
        "output": final_output['output'],
        "created": datetime.datetime.now(),
        "template": {
            "conversation": None
        },
        "displayable": show_displayable,
        "mapped_features": {"Prompt Tokens": prompt_token, "Completion Tokens": completion_token},
    }
    try:
        message_id = sqltool.write_response(data=data)
    except Exception as e:
        logger.error(f"sqltool.write_response : Error: {e} ")
        return {"error": e}
    return {
        "output": final_output['output'],
        "created": datetime.datetime.now(),
        "template": {
            "conversation": None
        },
        "displayable_data": show_displayable,
        "mapped_features": {"Prompt Tokens": prompt_token, "Completion Tokens": completion_token},
        "message_id": message_id,
        "chat_id": chat_id  # Include chat_id in the response
    }


@router.post("/user_ask_stream")
async def get_users(
    request: UserAskRequest, token: Annotated[str, Depends(oauth2_scheme)]
):
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    stream_obj = calling_agent.ReplyStream()

    if user_id is not None:
        # Get chat_id from request, or find active chat, or generate a new one
        chat_id = request.chat_id

        # If no chat_id provided, try to find an active one for this user and symbol
        if not chat_id:
            # Check if user already has an active chat for this symbol
            active_chat_id = sqltool.get_active_chat_id(user_id, request.symbol)

            if active_chat_id:
                # Use the existing active chat
                chat_id = active_chat_id
                logger.info(f"Using existing chat_id: {chat_id}")
            else:
                # No active chat found, generate a new one with symbol
                chat_id = sqltool.generate_chat_id(user_id, request.symbol)
                logger.info(f"Generated new chat_id: {chat_id}")

        history_coversation = orch.history_conversation(
            symbol=request.symbol, user_id=user_id, SqlTool=sqltool, chat_id=chat_id
        )

        generator = stream_obj.generate(
            request.task,
            request.symbol,
            user_id,
            request.report,
            request.deep_search,
            history=history_coversation,
            chat_id=chat_id,
            longterm=request.longterm,
            verbose=True
        )
        print("history_coversation: ", history_coversation)

    else:
        return {"error": "token expired"}


    async def event_stream():
        conversation = ""

        async for chunk in generator:

                conversation += str(chunk)

                yield str(chunk)

        final_output = conversation



        # show_displayable = orch.Displayable_data(conversation=stream_obj.full_message_list)
        # token_usage = orch.token_count(conversation=stream_obj.full_message_list)
        # Write data to db
        data = {
            "input": request.task,
            "symbol": request.symbol,
            "user_id": user_id,
            "chat_id": chat_id,  # Include chat_id in the data
            "output": final_output,
            "created": datetime.datetime.now(),
            "template": {
                "conversation": None
            },  # have to solve this thing on monday error TypeError: Object of type RequestUsage is not JSON serializable
            "displayable": None,
            "mapped_features": {"Prompt Tokens": stream_obj.prompt_token, "Completion Tokens": stream_obj.completion_token},
        }
        try:
            message_id = sqltool.write_response(data=data)
            logger.info(message_id)
        except Exception as e:
            logger.error(f"sqltool.write_response : Error: {e} ")
            # return {"error": e}

    return StreamingResponse(event_stream(), media_type="text/plain")


@router.get("/user_chats")
async def get_user_chats(token: Annotated[str, Depends(oauth2_scheme)]):
    """
    Get all chat sessions for the authenticated user
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)

    if user_id is not None:
        chat_ids = sqltool.get_user_chats(user_id)
        return {"user_id": user_id, "chats": chat_ids}
    else:
        return {"error": "token expired"}


@router.get("/chat/{chat_id}")
async def get_chat_details(chat_id: str, token: Annotated[str, Depends(oauth2_scheme)]):
    """
    Get details of a specific chat session
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)

    if user_id is not None:
        chat_details = sqltool.get_chat_details(chat_id)

        # Verify this chat belongs to the authenticated user
        if chat_details and chat_details[0]["user_id"] == user_id:
            return {"chat_id": chat_id, "messages": chat_details}
        else:
            return {"error": "Chat not found or access denied"}
    else:
        return {"error": "token expired"}


@router.post("/new_chat")
async def create_new_chat(
    request: UserAskRequest, token: Annotated[str, Depends(oauth2_scheme)]
):
    """
    Explicitly create a new chat session
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    if user_id is not None:
        # Always generate a new chat_id
        chat_id = sqltool.generate_chat_id(user_id, request.symbol)
        logger.info(f"Created new chat_id: {chat_id}")

        # Return the new chat_id
        return {
            "user_id": user_id,
            "symbol": request.symbol,
            "chat_id": chat_id,
            "created": datetime.datetime.now().isoformat()
        }
    else:
        return {"error": "token expired"}


@router.get("/memories")
async def get_user_memories(
    limit: int = 100,
    token: Annotated[str, Depends(oauth2_scheme)] = None
):
    """
    Get all long-term memories for the authenticated user
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    
    if user_id is None:
        return {"error": "token expired"}
    
    if not is_mem0_available():
        return {"error": "Memory system (mem0) is not available"}
    
    # Validate limit
    if limit > 500:
        limit = 500
    elif limit < 1:
        limit = 1
    
    try:
        memories = await get_all_mem0_memories(user_id=user_id, limit=limit)
        
        return {
            "user_id": user_id,
            "total_memories": len(memories),
            "limit": limit,
            "memories": memories
        }
    
    except Exception as e:
        logger.error(f"Error retrieving memories for user {user_id}: {e}")
        return {"error": f"Failed to retrieve memories: {str(e)}"}


@router.delete("/memories/{memory_id}")
async def delete_user_memory(
    memory_id: str,
    token: Annotated[str, Depends(oauth2_scheme)]
):
    """
    Delete a specific memory by ID for the authenticated user
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    
    if user_id is None:
        return {"error": "token expired"}
    
    if not is_mem0_available():
        return {"error": "Memory system (mem0) is not available"}
    
    if not memory_id:
        return {"error": "memory_id is required"}
    
    try:
        success = await delete_mem0_memory(memory_id=memory_id, user_id=user_id)
        
        if success:
            return {
                "message": f"Memory {memory_id} deleted successfully",
                "memory_id": memory_id,
                "user_id": user_id
            }
        else:
            return {"error": f"Failed to delete memory {memory_id}"}
    
    except Exception as e:
        logger.error(f"Error deleting memory {memory_id} for user {user_id}: {e}")
        return {"error": f"Failed to delete memory: {str(e)}"}


@router.delete("/memories")
async def delete_all_user_memories(
    token: Annotated[str, Depends(oauth2_scheme)]
):
    """
    Delete ALL memories for the authenticated user (use with caution!)
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    
    if user_id is None:
        return {"error": "token expired"}
    
    if not is_mem0_available():
        return {"error": "Memory system (mem0) is not available"}
    
    try:
        success = await delete_all_mem0_memories(user_id=user_id)
        
        if success:
            return {
                "message": f"All memories deleted successfully for user {user_id}",
                "user_id": user_id
            }
        else:
            return {"error": f"Failed to delete all memories for user {user_id}"}
    
    except Exception as e:
        logger.error(f"Error deleting all memories for user {user_id}: {e}")
        return {"error": f"Failed to delete all memories: {str(e)}"}


@router.get("/memories/count")
async def get_user_memory_count(
    token: Annotated[str, Depends(oauth2_scheme)]
):
    """
    Get the total count of memories for the authenticated user
    """
    user_id = token_decode.token_decoder(token, settings.DECODER_SECRET_KEY)
    
    if user_id is None:
        return {"error": "token expired"}
    
    if not is_mem0_available():
        return {"error": "Memory system (mem0) is not available"}
    
    try:
        # Get all memories with a high limit to count them
        memories = await get_all_mem0_memories(user_id=user_id, limit=10000)
        
        return {
            "user_id": user_id,
            "total_memories": len(memories)
        }
    
    except Exception as e:
        logger.error(f"Error counting memories for user {user_id}: {e}")
        return {"error": f"Failed to count memories: {str(e)}"}
