"""
Test script for mem0 integration in generate_reply.py
"""
import asyncio
import datetime
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.memory.mem0_helper import (
    search_mem0_memories,
    store_mem0_memory,
    is_mem0_available,
    format_mem0_context
)
from app.memory.memory_manager import search_memories, format_memory_context
from app.logging_custom_directory.logger_custom import logger

# Set up logging
logging.basicConfig(level=logging.INFO)

async def test_mem0_integration():
    """
    Test the mem0 integration functionality that's used in generate_reply.py
    """
    logger.info("=== Testing Mem0 Integration for generate_reply.py ===")
    
    # Test data
    user_id = "test_generate_reply_user"
    chat_id = f"chat_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # Check if mem0 is available
    logger.info(f"Mem0 available: {is_mem0_available()}")
    
    # Test 1: Store some initial conversations
    logger.info("\n--- Test 1: Storing initial conversations ---")
    
    conversations = [
        {
            "task": "What do you think about Tesla stock for long-term investment?",
            "response": "Tesla (TSLA) is a leading electric vehicle manufacturer with strong growth potential. For long-term investment, consider its innovation in autonomous driving, energy storage, and global expansion. However, the stock can be volatile due to market sentiment and regulatory changes.",
            "symbol": "TSLA"
        },
        {
            "task": "Should I invest in dividend-paying stocks for passive income?",
            "response": "Dividend-paying stocks can be excellent for passive income. Consider dividend aristocrats like Johnson & Johnson (JNJ), Coca-Cola (KO), and Procter & Gamble (PG). These companies have consistently increased dividends for 25+ years. Also look at REITs and utility stocks for higher yields.",
            "symbol": "JNJ"
        },
        {
            "task": "What's your opinion on cryptocurrency investments?",
            "response": "Cryptocurrency can be a high-risk, high-reward investment. Bitcoin and Ethereum are the most established. Consider only investing what you can afford to lose, and diversify across traditional assets as well. The market is highly volatile and regulatory changes can impact prices significantly.",
            "symbol": "BTC"
        }
    ]
    
    for i, conv in enumerate(conversations, 1):
        messages = [
            {"role": "user", "content": conv["task"]},
            {"role": "assistant", "content": conv["response"]}
        ]
        
        if is_mem0_available():
            success = await store_mem0_memory(
                user_id=user_id,
                messages=messages,
                chat_id=chat_id,
                metadata={"symbol": conv["symbol"], "timestamp": datetime.datetime.now().isoformat()}
            )
            logger.info(f"Stored conversation {i} in mem0: {success}")
        
        # Small delay to ensure different timestamps
        await asyncio.sleep(0.5)
    
    # Wait for processing
    await asyncio.sleep(2)
    
    # Test 2: Search using mem0 (simulating what happens in generate_reply.py)
    logger.info("\n--- Test 2: Searching with mem0 (like in generate_reply.py) ---")
    
    test_queries = [
        "Tesla electric vehicle investment",
        "dividend stocks for income",
        "cryptocurrency Bitcoin investment",
        "long-term investment strategy"
    ]
    
    for query in test_queries:
        logger.info(f"\nSearching for: '{query}'")
        
        # Search using mem0 if available
        memory_context = ""
        
        if is_mem0_available():
            try:
                mem0_memories = await search_mem0_memories(
                    user_id=user_id,
                    query=query,
                    chat_id=chat_id,
                    limit=3
                )
                if mem0_memories:
                    mem0_context = format_mem0_context(mem0_memories)
                    memory_context += mem0_context
                    logger.info(f"Found {len(mem0_memories)} memories using mem0")
                    logger.info(f"Mem0 context: {mem0_context[:200]}...")
            except Exception as e:
                logger.error(f"Error searching with mem0: {e}")
        
        # Search using existing memory system
        try:
            existing_memory_results = await search_memories(
                user_id=user_id,
                query=query,
                limit=3,
                include_conversation_history=True,
                chat_id=chat_id
            )
            if existing_memory_results and existing_memory_results.get("results"):
                existing_context = format_memory_context(existing_memory_results, include_answers=True)
                memory_context += existing_context
                logger.info(f"Found {len(existing_memory_results.get('results', []))} memories using existing system")
                logger.info(f"Existing context: {existing_context[:200]}...")
        except Exception as e:
            logger.error(f"Error searching with existing memory system: {e}")
        
        # Show combined context (what would be sent to the agent)
        if memory_context:
            logger.info(f"Combined memory context length: {len(memory_context)} characters")
        else:
            logger.info("No memory context found")
    
    # Test 3: Simulate the full generate_reply.py flow
    logger.info("\n--- Test 3: Simulating full generate_reply.py flow ---")
    
    new_task = "I'm looking for safe investments with steady returns. What do you recommend?"
    symbol = "SPY"
    
    logger.info(f"New task: {new_task}")
    logger.info(f"Symbol: {symbol}")
    
    # Step 1: Search for relevant memories (both mem0 and existing)
    memory_context = ""
    
    # Search using mem0 if available
    if is_mem0_available():
        try:
            mem0_memories = await search_mem0_memories(
                user_id=user_id,
                query=new_task,
                chat_id=chat_id,
                limit=3
            )
            if mem0_memories:
                mem0_context = format_mem0_context(mem0_memories)
                memory_context += mem0_context
                logger.info(f"Found {len(mem0_memories)} memories using mem0 for new task")
        except Exception as e:
            logger.error(f"Error searching with mem0: {e}")
    
    # Search using existing memory system
    try:
        existing_memory_results = await search_memories(
            user_id=user_id,
            query=new_task,
            limit=3,
            include_conversation_history=True,
            chat_id=chat_id
        )
        if existing_memory_results and existing_memory_results.get("results"):
            existing_context = format_memory_context(existing_memory_results, include_answers=True)
            memory_context += existing_context
            logger.info(f"Found {len(existing_memory_results.get('results', []))} memories using existing system for new task")
    except Exception as e:
        logger.error(f"Error searching with existing memory system: {e}")
    
    # Step 2: Create enhanced task with memory context
    if memory_context:
        enhanced_task = f"{new_task}\n\n{memory_context}"
        logger.info(f"Enhanced task with memory context (length: {len(enhanced_task)})")
        logger.info(f"Enhanced task preview: {enhanced_task[:300]}...")
    else:
        enhanced_task = new_task
        logger.info("No memory context available, using original task")
    
    # Step 3: Simulate response generation (we'll just create a mock response)
    mock_response = "For safe investments with steady returns, consider a diversified portfolio including index funds like SPY, dividend-paying stocks, and bonds. Based on your previous interest in dividend stocks, you might also consider REITs and utility stocks for consistent income."
    
    # Step 4: Store the new conversation
    try:
        messages = [
            {"role": "user", "content": new_task},
            {"role": "assistant", "content": mock_response}
        ]
        
        # Store using mem0 if available
        if is_mem0_available():
            success = await store_mem0_memory(
                user_id=user_id,
                messages=messages,
                chat_id=chat_id,
                metadata={"symbol": symbol, "timestamp": datetime.datetime.now().isoformat()}
            )
            logger.info(f"Stored new conversation in mem0: {success}")
        else:
            logger.warning("Mem0 not available for storing conversation")
            
    except Exception as e:
        logger.error(f"Error storing conversation in mem0: {e}")
    
    logger.info("\n=== Mem0 integration test completed! ===")

async def test_memory_context_formatting():
    """
    Test the memory context formatting functions.
    """
    logger.info("\n=== Testing Memory Context Formatting ===")
    
    # Mock mem0 memories
    mock_mem0_memories = [
        {
            "memory": "User is interested in Tesla stock for long-term investment. Discussed growth potential and volatility.",
            "score": 0.85,
            "metadata": {"symbol": "TSLA"}
        },
        {
            "memory": "User asked about dividend-paying stocks for passive income. Recommended dividend aristocrats.",
            "score": 0.78,
            "metadata": {"symbol": "JNJ"}
        }
    ]
    
    # Test mem0 context formatting
    mem0_context = format_mem0_context(mock_mem0_memories)
    logger.info(f"Mem0 context formatting:")
    logger.info(mem0_context)
    
    # Mock existing memory results
    mock_existing_results = {
        "results": [
            {
                "memory": "What do you think about cryptocurrency investments?",
                "messages": [
                    {"role": "user", "content": "What do you think about cryptocurrency investments?"},
                    {"role": "assistant", "content": "Cryptocurrency can be high-risk, high-reward. Bitcoin and Ethereum are most established."}
                ],
                "score": 0.82
            }
        ]
    }
    
    # Test existing context formatting
    existing_context = format_memory_context(mock_existing_results, include_answers=True)
    logger.info(f"\nExisting context formatting:")
    logger.info(existing_context)
    
    # Test combined context
    combined_context = mem0_context + existing_context
    logger.info(f"\nCombined context length: {len(combined_context)} characters")

if __name__ == "__main__":
    async def main():
        await test_mem0_integration()
        await test_memory_context_formatting()
    
    asyncio.run(main())
