# Long term Mem0 Memory Management API Documentation

## Overview

This document describes the new memory management system implemented for the TraderGPT application. The system provides full CRUD (Create, Read, Update, Delete) operations for long-term memories using the mem0 memory system.

## What's New

### 1. Enhanced Memory Control
- **New Parameter**: `search_longterm` - Controls whether long-term memories are searched during conversations
- **Memory Management**: Full API endpoints for managing user memories
- **User Isolation**: Each user can only access and manage their own memories

### 2. New API Endpoints
Four new endpoints have been added for comprehensive memory management:
- `GET /api/memories` - Retrieve user memories
- `DELETE /api/memories/{memory_id}` - Delete specific memory
- `DELETE /api/memories` - Delete all user memories  
- `GET /api/memories/count` - Get memory count

---

## API Endpoints Documentation

### Authentication
All endpoints require authentication via JW<PERSON> token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### 1. Get User Memories
**Endpoint**: `GET /api/memories`

**Description**: Retrieve all long-term memories for the authenticated user

**Query Parameters**:
- `limit` (optional): Maximum number of memories to retrieve
  - Default: 100
  - Maximum: 500
  - Minimum: 1

**Response**:
```json
{
  "user_id": "user123",
  "total_memories": 15,
  "limit": 100,
  "memories": [
    {
      "id": "mem_123abc",
      "memory": "User prefers technical analysis over fundamental analysis for AAPL",
      "metadata": {
        "user_id": "user123",
        "source": "tradergpt",
        "memory_type": "conversation",
        "chat_id": "chat_456"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "source": "mem0"
    }
  ]
}
```

**Example**:
```bash
curl -X GET "http://localhost:8000/api/memories?limit=50" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 2. Delete Specific Memory
**Endpoint**: `DELETE /api/memories/{memory_id}`

**Description**: Delete a specific memory by its ID

**Path Parameters**:
- `memory_id`: The unique ID of the memory to delete

**Response**:
```json
{
  "message": "Memory mem_123abc deleted successfully",
  "memory_id": "mem_123abc",
  "user_id": "user123"
}
```

**Example**:
```bash
curl -X DELETE "http://localhost:8000/api/memories/mem_123abc" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 3. Delete All User Memories
**Endpoint**: `DELETE /api/memories`

**Description**: Delete ALL memories for the authenticated user (use with caution!)

**Response**:
```json
{
  "message": "All memories deleted successfully for user user123",
  "user_id": "user123"
}
```

**Example**:
```bash
curl -X DELETE "http://localhost:8000/api/memories" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 4. Get Memory Count
**Endpoint**: `GET /api/memories/count`

**Description**: Get the total count of memories for the authenticated user

**Response**:
```json
{
  "user_id": "user123",
  "total_memories": 42
}
```

**Example**:
```bash
curl -X GET "http://localhost:8000/api/memories/count" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

---

## Enhanced Conversation API

### New Parameter: `search_longterm`

Both conversation endpoints now support the `search_longterm` parameter:

#### Modified Endpoints:
- `POST /api/user_ask`
- `POST /api/user_ask_stream`

#### New Request Schema:
```json
{
  "task": "What's the latest on Apple?",
  "symbol": "AAPL",
  "chat_id": "chat_456", 
  "report": false,
  "deep_search": false,
  "search_longterm": true  // NEW PARAMETER
}
```

#### Parameter Details:
- **Type**: `boolean`
- **Default**: `true`
- **Description**: Whether to search long-term memories using mem0
- **When `true`**: System searches user's long-term memories for relevant context
- **When `false`**: System skips memory search, potentially faster responses but no historical context

#### Examples:

**With Long-term Memory (default)**:
```bash
curl -X POST "http://localhost:8000/api/user_ask" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "What do you think about AAPL?",
    "symbol": "AAPL",
    "search_longterm": true
  }'
```

**Without Long-term Memory**:
```bash
curl -X POST "http://localhost:8000/api/user_ask" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "What do you think about AAPL?",
    "symbol": "AAPL", 
    "search_longterm": false
  }'
```

---

## Error Handling

### Common Error Responses:

#### Authentication Errors:
```json
{
  "error": "token expired"
}
```

#### System Unavailable:
```json
{
  "error": "Memory system (mem0) is not available"
}
```

#### Validation Errors:
```json
{
  "error": "memory_id is required"
}
```

#### Operation Failures:
```json
{
  "error": "Failed to delete memory mem_123abc"
}
```

---

## Frontend Integration Guide

### 1. Memory Management Dashboard

Create a dashboard to display and manage user memories:

```javascript
// Fetch user memories
async function getUserMemories(limit = 100) {
  const response = await fetch(`/api/memories?limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  return await response.json();
}

// Delete specific memory
async function deleteMemory(memoryId) {
  const response = await fetch(`/api/memories/${memoryId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  return await response.json();
}

// Get memory count for pagination
async function getMemoryCount() {
  const response = await fetch('/api/memories/count', {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  return await response.json();
}
```

### 2. Memory Toggle in Chat Interface

Add a toggle to control long-term memory usage:

```javascript
// Chat with memory control
async function sendMessage(message, symbol, useMemory = true) {
  const response = await fetch('/api/user_ask', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      task: message,
      symbol: symbol,
      search_longterm: useMemory,
      report: false,
      deep_search: false
    })
  });
  return await response.json();
}
```

### 3. Memory Management Components

**React Example**:
```jsx
import React, { useState, useEffect } from 'react';

const MemoryManager = () => {
  const [memories, setMemories] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadMemories = async () => {
    setLoading(true);
    const data = await getUserMemories();
    setMemories(data.memories || []);
    setLoading(false);
  };

  const handleDeleteMemory = async (memoryId) => {
    if (confirm('Are you sure you want to delete this memory?')) {
      await deleteMemory(memoryId);
      loadMemories(); // Refresh list
    }
  };

  useEffect(() => {
    loadMemories();
  }, []);

  return (
    <div className="memory-manager">
      <h2>Your Memories</h2>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="memory-list">
          {memories.map(memory => (
            <div key={memory.id} className="memory-item">
              <p>{memory.memory}</p>
              <small>Created: {memory.created_at}</small>
              <button onClick={() => handleDeleteMemory(memory.id)}>
                Delete
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

---

## Security Features

### 1. Authentication Required
- All endpoints require valid JWT authentication
- Tokens are validated on every request

### 2. User Isolation
- Users can only access their own memories
- Memory operations are scoped to the authenticated user
- No cross-user data access possible

### 3. Input Validation
- Memory ID validation for delete operations
- Limit validation for memory retrieval
- Proper error handling for invalid requests

### 4. Availability Checks
- System verifies mem0 availability before operations
- Graceful degradation when memory system is unavailable

---

## Technical Implementation Details

### Files Modified/Created:

1. **`app/memory/mem0_helper.py`**:
   - Added `get_all_memories()` method
   - Added `delete_memory()` method  
   - Added `delete_all_memories()` method
   - Added convenience functions

2. **`app/api/api_payload_schema.py`**:
   - Added `search_longterm` parameter to `UserAskRequest`
   - Added `DeleteMemoryRequest` schema
   - Added `GetMemoriesRequest` schema

3. **`app/api/generate_reply.py`**:
   - Updated conversation endpoints to use `search_longterm`
   - Added 4 new memory management endpoints
   - Added proper error handling and logging

4. **`app/agent/calling_agent.py`**:
   - Updated `generating_reply()` function with `search_longterm` parameter
   - Updated `ReplyStream.generate()` method with `search_longterm` parameter
   - Added conditional memory search logic

### Memory Data Structure:
```python
{
    "id": "unique_memory_id",
    "memory": "memory_content_text", 
    "metadata": {
        "user_id": "user123",
        "source": "tradergpt",
        "memory_type": "conversation",
        "chat_id": "chat_456"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "source": "mem0"
}
```

---

## Testing the Implementation

### 1. Test Memory Creation
1. Have a conversation with the system
2. Check if memories are created automatically
3. Use `GET /api/memories` to verify

### 2. Test Memory Retrieval
1. Send requests with `search_longterm: true`
2. Verify relevant memories are included in responses
3. Test with `search_longterm: false` for comparison

### 3. Test Memory Deletion
1. Get memory list
2. Delete specific memories
3. Verify they're removed from subsequent requests

### 4. Test Error Handling
1. Try accessing without authentication
2. Try deleting non-existent memories
3. Test with invalid parameters

---

## Best Practices

### For Frontend Developers:
1. **Always handle authentication errors** - Redirect to login when token expires
2. **Implement confirmation dialogs** for delete operations
3. **Show loading states** during memory operations
4. **Paginate memory lists** for better performance
5. **Cache memory count** to avoid frequent API calls

### For Backend Developers:
1. **Always validate user permissions** before memory operations
2. **Log all memory operations** for debugging
3. **Handle mem0 unavailability gracefully**
4. **Use appropriate HTTP status codes**
5. **Implement rate limiting** for memory endpoints

### For System Administrators:
1. **Monitor mem0 system health**
2. **Set up proper backup strategies** for memory data
3. **Configure appropriate limits** for memory retrieval
4. **Monitor API usage patterns**

---

## Troubleshooting

### Common Issues:

1. **"Memory system (mem0) is not available"**
   - Check if mem0 service is running
   - Verify Qdrant connection
   - Check mem0 configuration

2. **"token expired"**
   - User needs to re-authenticate
   - Check JWT token validation

3. **Empty memory responses**
   - User might not have any stored memories
   - Check if conversations are being stored properly

4. **Memory deletion fails**
   - Verify memory ID exists
   - Check user permissions
   - Check mem0 service status

---