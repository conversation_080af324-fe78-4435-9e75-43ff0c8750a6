"""
Test script for the LangMem memory system with direct Qdrant integration.
"""
import asyncio
import datetime
import logging
import os
import sys

from app.memory.memory_manager import store_user_message, search_memories
from app.memory.init_qdrant import init_qdrant_collection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_memory_system():
    """
    Test the LangMem memory system.
    """
    # Initialize Qdrant collection
    init_qdrant_collection()
    
    # Create test user and chat IDs
    user_id = "test_user_123"
    chat_id = f"{user_id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # Test storing a message
    logger.info("Testing storing a message...")
    messages = [
        {"role": "user", "content": "What do you think about Tesla stock?"},
        {"role": "assistant", "content": "Tesla (TSLA) is a leading electric vehicle manufacturer with significant growth potential but also faces increasing competition."}
    ]
    success = await store_user_message(
        user_id=user_id,
        chat_id=chat_id,
        messages=messages,
        run_async=False
    )
    logger.info(f"Stored message: {success}")
    
    # Wait a moment for the memory to be processed
    logger.info("Waiting for memory processing...")
    await asyncio.sleep(2)
    
    # Test searching memories
    logger.info("Testing searching memories...")
    memory_results = await search_memories(
        user_id=user_id,
        query="Tesla electric vehicles",
        limit=5,
        include_conversation_history=True,
        chat_id=chat_id
    )
    
    logger.info(f"Found {len(memory_results.get('results', []))} memories")
    for memory in memory_results.get("results", []):
        logger.info(f"Memory: {memory['memory']}")
        logger.info(f"Score: {memory['score']}")
    
    # Test storing another message
    logger.info("Testing storing another message...")
    messages = [
        {"role": "user", "content": "What about Apple stock?"},
        {"role": "assistant", "content": "Apple (AAPL) is a technology giant with a strong ecosystem and consistent performance."}
    ]
    success = await store_user_message(
        user_id=user_id,
        chat_id=chat_id,
        messages=messages,
        run_async=False
    )
    logger.info(f"Stored message: {success}")
    
    # Wait a moment for the memory to be processed
    logger.info("Waiting for memory processing...")
    await asyncio.sleep(2)
    
    # Test searching memories again
    logger.info("Testing searching memories again...")
    memory_results = await search_memories(
        user_id=user_id,
        query="Apple stock",
        limit=5,
        include_conversation_history=True,
        chat_id=chat_id
    )
    
    logger.info(f"Found {len(memory_results.get('results', []))} memories")
    for memory in memory_results.get("results", []):
        logger.info(f"Memory: {memory['memory']}")
        logger.info(f"Score: {memory['score']}")
    
    logger.info("Memory system tests completed successfully!")

if __name__ == "__main__":
    asyncio.run(test_memory_system())
